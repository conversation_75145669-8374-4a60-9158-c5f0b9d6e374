#!/usr/bin/env python3
"""
将轨迹文件转换为HTML可视化文件
"""

import os
import sys
from ase.io import read
from x3dase.x3d import X3D

def convert_trajectory_to_html(traj_file, output_html=None):
    """
    将轨迹文件转换为HTML可视化
    
    Args:
        traj_file: 输入轨迹文件路径
        output_html: 输出HTML文件路径 (可选，默认基于输入文件名)
    """
    
    # 检查输入文件是否存在
    if not os.path.exists(traj_file):
        print(f"错误: 轨迹文件不存在: {traj_file}")
        return False
    
    # 设置输出文件名
    if output_html is None:
        base_name = os.path.splitext(traj_file)[0]
        output_html = f"{base_name}.html"
    
    print(f"正在读取轨迹文件: {traj_file}")
    
    try:
        # 读取轨迹文件
        structures = read(traj_file, ":")
        print(f"✓ 成功读取 {len(structures)} 帧")
        print(f"✓ 每帧包含 {len(structures[0])} 个原子")
        
        # 显示元素组成
        symbols = structures[0].get_chemical_symbols()
        from collections import Counter
        element_count = Counter(symbols)
        print(f"✓ 元素组成: {dict(element_count)}")
        
    except Exception as e:
        print(f"错误: 无法读取轨迹文件: {e}")
        return False
    
    print(f"\n正在生成HTML可视化文件: {output_html}")
    
    try:
        # 创建X3D可视化对象
        x3d_viz = X3D(structures)
        
        # 写入HTML文件
        x3d_viz.write(output_html)
        
        print(f"✓ 成功生成HTML文件: {output_html}")
        print(f"✓ 文件大小: {os.path.getsize(output_html) / 1024:.1f} KB")
        
        # 提供使用建议
        print(f"\n使用建议:")
        print(f"1. 在浏览器中打开: {os.path.abspath(output_html)}")
        print(f"2. 使用VS Code预览: 右键 → 'Open with Live Server' 或 'Preview'")
        print(f"3. 支持的功能:")
        print(f"   - 3D旋转和缩放")
        print(f"   - 播放轨迹动画")
        print(f"   - 调整播放速度")
        print(f"   - 暂停/继续播放")
        
        return True
        
    except Exception as e:
        print(f"错误: 无法生成HTML文件: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("轨迹文件HTML可视化转换工具")
    print("="*60)
    
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("使用方法:")
        print(f"  {sys.argv[0]} <轨迹文件> [输出HTML文件]")
        print(f"\n示例:")
        print(f"  {sys.argv[0]} combined_trajectory.traj")
        print(f"  {sys.argv[0]} combined_trajectory.traj my_animation.html")
        return
    
    traj_file = sys.argv[1]
    output_html = sys.argv[2] if len(sys.argv) > 2 else None
    
    print(f"输入轨迹文件: {traj_file}")
    if output_html:
        print(f"输出HTML文件: {output_html}")
    else:
        base_name = os.path.splitext(traj_file)[0]
        print(f"输出HTML文件: {base_name}.html (自动命名)")
    
    print("-" * 60)
    
    success = convert_trajectory_to_html(traj_file, output_html)
    
    if success:
        print(f"\n✓ 转换完成!")
    else:
        print(f"\n✗ 转换失败!")

if __name__ == "__main__":
    main()
