"""
ML-NEB: Machine Learning Enhanced Nudged Elastic Band Calculations

A Python package for performing NEB (Nudged Elastic Band) calculations 
using machine learning potentials, particularly FAIRChem models.
"""

from .core.analyzer import NEBAnalyzer
from .utils.trajectory import combine_vasp_to_trajectory, analyze_trajectory, validate_trajectory_endpoints
from .visualization.html import convert_trajectory_to_html, create_neb_visualization

__version__ = "0.1.0"
__author__ = "ML-NEB Team"

__all__ = [
    "NEBAnalyzer",
    "combine_vasp_to_trajectory",
    "analyze_trajectory",
    "validate_trajectory_endpoints",
    "convert_trajectory_to_html",
    "create_neb_visualization",
]
