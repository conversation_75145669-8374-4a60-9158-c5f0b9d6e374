#!/usr/bin/env python3
"""
将totraj目录下的VASP POSCAR文件合并成一个ASE轨迹文件

使用方法:
python -m mlneb.scripts.combine_trajectory [选项]

选项:
--input-dir: 输入文件目录 (默认: totraj)
--output-file: 输出轨迹文件路径 (默认: combined_trajectory.traj)
--start: 起始文件编号 (默认: 1)
--end: 结束文件编号 (默认: 9)

示例:
python -m mlneb.scripts.combine_trajectory
python -m mlneb.scripts.combine_trajectory --input-dir my_vasp_files --output-file my_trajectory.traj
"""

from ..cli.combine_cli import main

if __name__ == "__main__":
    main()
