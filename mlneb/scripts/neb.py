#!/usr/bin/env python3
"""
优化的NEB计算脚本
=================

功能:
1. 加载初态和终态结构
2. 分析结构信息和周期边界条件
3. 比较原子位置变化
4. 执行NEB计算
5. 生成可视化文件和能量图

使用方法:
python -m mlneb.scripts.neb [reaction_label] [--trajectory-file TRAJ_FILE] [--final-file FINAL_FILE]

参数:
- reaction_label: 反应标签 (默认: r11)
  脚本会自动查找 data/{reaction_label}/ini.vasp 和 data/{reaction_label}/fin.vasp
- --trajectory-file, --traj: 外部轨迹文件路径 (可选)
  如果提供，将使用该文件中的帧而非内置插值算法
- --final-file: 自定义终态文件路径 (默认: data/{reaction_label}/fin.vasp)

模型配置:
- 使用本地预下载的检查点文件 (uma_sm.pt)
- 可以通过修改 checkpoint_path 参数使用其他本地模型文件
- 支持 FAIRChem 格式的所有预训练模型

输出文件:
- neb_analysis_report.txt: 详细分析报告
- data/{reaction_label}/interpolated_frames.html: 插值帧可视化
- data/{reaction_label}/{reaction_label}.traj: NEB轨迹文件
- data/{reaction_label}/reaction_coordinate.png: 反应坐标图
- data/{reaction_label}/{reaction_label}_neb.html: 优化后NEB可视化

示例:
python -m mlneb.scripts.neb r11  # 分析 data/r11/ini.vasp 和 data/r11/fin.vasp (使用内置插值)
python -m mlneb.scripts.neb r25  # 分析 data/r25/ini.vasp 和 data/r25/fin.vasp (使用内置插值)
python -m mlneb.scripts.neb r11 --traj my_trajectory.traj  # 使用外部轨迹文件
python -m mlneb.scripts.neb r11 --trajectory-file data/r11/custom_path.traj  # 使用完整路径的外部轨迹文件
"""

from ..cli.neb_cli import main

if __name__ == "__main__":
    main()
