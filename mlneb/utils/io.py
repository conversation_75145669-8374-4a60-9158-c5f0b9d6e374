"""
Input/Output utilities for ML-NEB package
"""

import os
from ase.io import read, write
from typing import List, Union
from ase import Atoms


def load_structures(file_path: str, index: Union[int, str, slice] = 0) -> Union[Atoms, List[Atoms]]:
    """
    Load atomic structures from file
    
    Args:
        file_path (str): Path to structure file
        index: Which structure(s) to load (0 for first, ":" for all, etc.)
        
    Returns:
        Atoms or List[Atoms]: Loaded structure(s)
        
    Raises:
        FileNotFoundError: If file doesn't exist
        Exception: If loading fails
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Structure file not found: {file_path}")
    
    try:
        structures = read(file_path, index=index)
        
        # Wrap structures for PBC
        if isinstance(structures, list):
            for structure in structures:
                structure.wrap()
        else:
            structures.wrap()
            
        return structures
    except Exception as e:
        raise Exception(f"Failed to load structures from {file_path}: {e}")


def save_structures(structures: Union[Atoms, List[Atoms]], file_path: str, format: str = None):
    """
    Save atomic structures to file
    
    Args:
        structures: Structure(s) to save
        file_path (str): Output file path
        format (str): File format (auto-detected if None)
        
    Raises:
        Exception: If saving fails
    """
    try:
        write(file_path, structures, format=format)
    except Exception as e:
        raise Exception(f"Failed to save structures to {file_path}: {e}")


def check_file_exists(file_path: str) -> bool:
    """
    Check if a file exists
    
    Args:
        file_path (str): Path to check
        
    Returns:
        bool: True if file exists
    """
    return os.path.exists(file_path)


def create_directory(dir_path: str) -> None:
    """
    Create directory if it doesn't exist
    
    Args:
        dir_path (str): Directory path to create
    """
    os.makedirs(dir_path, exist_ok=True)
