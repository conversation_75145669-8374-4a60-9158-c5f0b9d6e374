"""
Trajectory handling utilities for ML-NEB package
"""

import os
import numpy as np
from typing import List, Optional
from ase import Atoms
from ase.io import read, write
from collections import Counter


def combine_vasp_to_trajectory(input_dir: str = "totraj", 
                              output_file: str = "combined_trajectory.traj",
                              file_range: tuple = (1, 9)) -> bool:
    """
    Combine multiple VASP POSCAR files into a single ASE trajectory file
    
    Args:
        input_dir (str): Directory containing VASP files
        output_file (str): Output trajectory file path
        file_range (tuple): Range of file indices to combine (start, end)
        
    Returns:
        bool: True if successful, False otherwise
    """
    
    # Generate input file list
    start, end = file_range
    input_files = [os.path.join(input_dir, str(i)) for i in range(start, end)]
    
    print("正在读取VASP文件并合并为轨迹文件...")
    
    # Check if files exist
    for i, file_path in enumerate(input_files, start):
        if not os.path.exists(file_path):
            print(f"错误: 文件 {file_path} 不存在")
            return False
    
    # Read all structures
    structures = []
    for i, file_path in enumerate(input_files, start):
        try:
            print(f"读取文件 {i}: {file_path}")
            structure = read(file_path, format='vasp')
            structures.append(structure)
            print(f"  ✓ 原子数: {len(structure)}")
            print(f"  ✓ 元素: {set(structure.get_chemical_symbols())}")
        except Exception as e:
            print(f"错误: 无法读取文件 {file_path}: {e}")
            return False
    
    # Validate structure consistency
    if not _validate_structure_consistency(structures):
        return False
    
    # Write trajectory file
    print(f"\n正在写入轨迹文件: {output_file}")
    try:
        write(output_file, structures)
        print(f"✓ 成功创建轨迹文件: {output_file}")
        print(f"✓ 包含 {len(structures)} 帧")
        
        # Verify trajectory file
        print("\n验证轨迹文件...")
        test_structures = read(output_file, ":")
        print(f"✓ 轨迹文件包含 {len(test_structures)} 帧")
        print(f"✓ 每帧包含 {len(test_structures[0])} 个原子")
        
        return True
        
    except Exception as e:
        print(f"错误: 无法写入轨迹文件: {e}")
        return False


def _validate_structure_consistency(structures: List[Atoms]) -> bool:
    """
    Validate that all structures in a list are consistent
    
    Args:
        structures (List[Atoms]): List of atomic structures
        
    Returns:
        bool: True if all structures are consistent
    """
    print("\n验证结构一致性...")
    ref_structure = structures[0]
    ref_symbols = ref_structure.get_chemical_symbols()
    ref_cell = ref_structure.get_cell()
    
    for i, structure in enumerate(structures[1:], 2):
        if len(structure) != len(ref_structure):
            print(f"错误: 文件 {i} 的原子数 ({len(structure)}) 与第一个文件不符 ({len(ref_structure)})")
            return False
        
        if structure.get_chemical_symbols() != ref_symbols:
            print(f"错误: 文件 {i} 的化学符号与第一个文件不符")
            return False
        
        # Check if cells are the same (allow small numerical errors)
        if not np.allclose(structure.get_cell(), ref_cell, atol=1e-6):
            print(f"警告: 文件 {i} 的晶胞参数与第一个文件略有不同")
    
    print("✓ 所有结构验证通过")
    return True


def analyze_trajectory(traj_file: str) -> dict:
    """
    Analyze a trajectory file and return basic statistics
    
    Args:
        traj_file (str): Path to trajectory file
        
    Returns:
        dict: Analysis results including frame count, atom count, elements, etc.
    """
    if not os.path.exists(traj_file):
        raise FileNotFoundError(f"轨迹文件不存在: {traj_file}")
    
    try:
        structures = read(traj_file, ":")
        
        # Basic statistics
        frame_count = len(structures)
        atom_count = len(structures[0]) if structures else 0
        
        # Element analysis
        if structures:
            symbols = structures[0].get_chemical_symbols()
            element_count = Counter(symbols)
        else:
            element_count = {}
        
        # Energy analysis (if available)
        energies = []
        for structure in structures:
            try:
                energy = structure.get_potential_energy()
                energies.append(energy)
            except:
                break
        
        analysis = {
            'frame_count': frame_count,
            'atom_count': atom_count,
            'element_count': dict(element_count),
            'has_energies': len(energies) > 0,
            'energy_range': (min(energies), max(energies)) if energies else None,
            'file_size_kb': os.path.getsize(traj_file) / 1024
        }
        
        return analysis
        
    except Exception as e:
        raise Exception(f"无法分析轨迹文件: {e}")


def validate_trajectory_endpoints(traj_file: str, 
                                initial_file: str, 
                                final_file: str) -> dict:
    """
    Validate that trajectory endpoints match reference structures
    
    Args:
        traj_file (str): Path to trajectory file
        initial_file (str): Path to initial structure file
        final_file (str): Path to final structure file
        
    Returns:
        dict: Validation results
    """
    try:
        # Load structures
        traj_structures = read(traj_file, ":")
        initial_ref = read(initial_file)
        final_ref = read(final_file)
        
        initial_traj = traj_structures[0]
        final_traj = traj_structures[-1]
        
        # Check consistency
        results = {
            'initial_atoms_match': len(initial_traj) == len(initial_ref),
            'final_atoms_match': len(final_traj) == len(final_ref),
            'initial_symbols_match': initial_traj.get_chemical_symbols() == initial_ref.get_chemical_symbols(),
            'final_symbols_match': final_traj.get_chemical_symbols() == final_ref.get_chemical_symbols(),
            'initial_cell_match': np.allclose(initial_traj.get_cell(), initial_ref.get_cell(), atol=1e-6),
            'final_cell_match': np.allclose(final_traj.get_cell(), final_ref.get_cell(), atol=1e-6),
        }
        
        results['all_match'] = all(results.values())
        
        return results
        
    except Exception as e:
        raise Exception(f"轨迹端点验证失败: {e}")
