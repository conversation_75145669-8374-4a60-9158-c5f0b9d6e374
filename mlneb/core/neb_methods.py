#!/usr/bin/env python3
"""
NEB计算方法模块
包含NEB计算、能量分析和可视化等核心方法
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from ase.optimize import BFGS
from ase.io import read
from fairchem.applications.cattsunami.core.autoframe import interpolate
from fairchem.core import FAIRChemCalculator
from fairchem.core.units.mlip_unit import load_predict_unit
from ase.mep import DyNEB
from x3dase.x3d import X3D


def run_neb_calculation(analyzer):
    """执行NEB计算"""
    analyzer._add_section("5. NEB计算")
    
    print("正在执行NEB计算...")
    
    # 初始化计算器
    analyzer.report.append("初始化计算器...")
    analyzer._write_report()  # 实时写入
    predictor = load_predict_unit(analyzer.checkpoint_path)
    analyzer.report.append(f"✓ 计算器初始化完成 (模型: {analyzer.checkpoint_path})")
    analyzer.report.append("")
    analyzer._write_report()  # 实时写入
    
    # 获取插值帧
    if analyzer.trajectory_file and os.path.exists(analyzer.trajectory_file):
        # 使用外部轨迹文件
        analyzer.report.append(f"使用外部轨迹文件: {analyzer.trajectory_file}")
        analyzer._write_report()
        
        frame_set = read(analyzer.trajectory_file, ":")
        if len(frame_set) < 3:
            raise ValueError(f"轨迹文件中帧数不足 (当前: {len(frame_set)}, 至少需要: 3)")
        
        analyzer.num_frames = len(frame_set)
        analyzer.report.append(f"✓ 从轨迹文件加载 {len(frame_set)} 帧")
        
        # 验证轨迹文件首末帧与参考结构的一致性
        analyzer.report.append("")
        analyzer.report.append("验证轨迹文件一致性:")
        initial_loaded = frame_set[0]
        final_loaded = frame_set[-1]
        
        # 检查原子数是否一致
        atoms_match = True
        if len(initial_loaded) != len(analyzer.initial_frame):
            analyzer.report.append(f"⚠️  首帧原子数不匹配: 轨迹({len(initial_loaded)}) vs 参考({len(analyzer.initial_frame)})")
            atoms_match = False
        if len(final_loaded) != len(analyzer.final_frame):
            analyzer.report.append(f"⚠️  末帧原子数不匹配: 轨迹({len(final_loaded)}) vs 参考({len(analyzer.final_frame)})")
            atoms_match = False
        
        # 检查化学符号是否一致
        if initial_loaded.get_chemical_symbols() != analyzer.initial_frame.get_chemical_symbols():
            analyzer.report.append("⚠️  首帧化学符号不匹配")
            atoms_match = False
        if final_loaded.get_chemical_symbols() != analyzer.final_frame.get_chemical_symbols():
            analyzer.report.append("⚠️  末帧化学符号不匹配")
            atoms_match = False
        
        if atoms_match:
            analyzer.report.append("✓ 轨迹文件首末帧与参考结构匹配")
        
    else:
        # 使用内置插值算法
        if analyzer.trajectory_file:
            analyzer.report.append(f"⚠️  指定的轨迹文件不存在: {analyzer.trajectory_file}")
            analyzer.report.append("回退到内置插值算法")
        else:
            analyzer.report.append("使用内置插值算法生成初始路径")
        
        analyzer.report.append(f"生成插值帧 (共 {analyzer.num_frames} 帧)...")
        analyzer._write_report()  # 实时写入
        frame_set = interpolate(analyzer.initial_frame, analyzer.final_frame, analyzer.num_frames)
        analyzer.report.append(f"✓ 使用内置算法生成 {len(frame_set)} 帧")
    
    # 保存插值帧可视化
    interpolated_html = os.path.join(analyzer.output_dir, "interpolated_frames.html")
    x3d_interpolated = X3D(frame_set)
    x3d_interpolated.write(interpolated_html)
    analyzer.report.append(f"✓ 插值帧可视化已保存: {interpolated_html}")
    analyzer.report.append("")
    analyzer._write_report()  # 实时写入
    
    # 设置NEB
    analyzer.report.append("设置NEB计算...")
    analyzer.report.append(f"  弹簧常数 k: {analyzer.k}")
    analyzer.report.append(f"  收敛标准 fmax: {analyzer.fmax} eV/Å")
    analyzer.report.append(f"  爬升镜像阈值: {analyzer.fmax + analyzer.delta_fmax_climb} eV/Å")
    analyzer.report.append(f"  随机种子: {analyzer.random_seed} ({analyzer.seed_source})")
    analyzer._write_report()  # 实时写入
    
    # 使用DyNEB代替OCPNEB
    neb = DyNEB(frame_set, k=analyzer.k)
    
    # 为每个镜像设置计算器
    for image in frame_set:
        image.calc = FAIRChemCalculator(predictor, task_name="oc20")
    
    # 优化
    trajectory_file = os.path.join(analyzer.output_dir, f"{analyzer.reaction_label}.traj")
    optimizer = BFGS(neb, trajectory=trajectory_file)
    
    # 记录收敛信息
    convergence_info = {
        'phase1': {'converged': False, 'steps': 0, 'final_fmax': None},
        'phase2': {'converged': False, 'steps': 0, 'final_fmax': None}
    }
    
    analyzer.report.append("")
    analyzer.report.append("开始优化 (第一阶段 - 无爬升镜像)...")
    analyzer.report.append(f"  目标收敛标准: {analyzer.fmax + analyzer.delta_fmax_climb:.4f} eV/Å")
    analyzer.report.append(f"  最大步数: {analyzer.max_steps_phase1}")
    analyzer._write_report()  # 实时写入
    
    # 获取初始受力信息
    initial_forces = neb.get_forces()
    initial_fmax = np.max(np.abs(initial_forces))
    analyzer.report.append(f"  初始最大受力: {initial_fmax:.4f} eV/Å")
    analyzer._write_report()
    
    conv1 = optimizer.run(fmax=analyzer.fmax + analyzer.delta_fmax_climb, steps=analyzer.max_steps_phase1)
    
    # 记录第一阶段结果
    convergence_info['phase1']['converged'] = conv1
    convergence_info['phase1']['steps'] = optimizer.nsteps
    final_forces_phase1 = neb.get_forces()
    final_fmax_phase1 = np.max(np.abs(final_forces_phase1))
    convergence_info['phase1']['final_fmax'] = final_fmax_phase1
    
    analyzer.report.append("")
    analyzer.report.append("第一阶段优化结果:")
    analyzer.report.append(f"  执行步数: {convergence_info['phase1']['steps']}")
    analyzer.report.append(f"  最终最大受力: {final_fmax_phase1:.4f} eV/Å")
    analyzer.report.append(f"  收敛标准: {analyzer.fmax + analyzer.delta_fmax_climb:.4f} eV/Å")
    
    if conv1:
        analyzer.report.append("  ✓ 第一阶段已收敛")
        analyzer.report.append("")
        analyzer.report.append("开始优化 (第二阶段 - 爬升镜像)...")
        analyzer.report.append(f"  目标收敛标准: {analyzer.fmax:.4f} eV/Å")
        analyzer.report.append(f"  最大步数: {analyzer.max_steps_phase2}")
        analyzer._write_report()  # 实时写入
        
        neb.climb = True
        # 重置优化器计数器
        optimizer.nsteps = 0
        conv2 = optimizer.run(fmax=analyzer.fmax, steps=analyzer.max_steps_phase2)
        
        # 记录第二阶段结果
        convergence_info['phase2']['converged'] = conv2
        convergence_info['phase2']['steps'] = optimizer.nsteps
        final_forces_phase2 = neb.get_forces()
        final_fmax_phase2 = np.max(np.abs(final_forces_phase2))
        convergence_info['phase2']['final_fmax'] = final_fmax_phase2
        
        analyzer.report.append("")
        analyzer.report.append("第二阶段优化结果:")
        analyzer.report.append(f"  执行步数: {convergence_info['phase2']['steps']}")
        analyzer.report.append(f"  最终最大受力: {final_fmax_phase2:.4f} eV/Å")
        analyzer.report.append(f"  收敛标准: {analyzer.fmax:.4f} eV/Å")
        
        if conv2:
            analyzer.report.append("  ✓ 第二阶段已收敛")
            analyzer.report.append("")
            analyzer.report.append("🎉 NEB计算完全收敛!")
        else:
            analyzer.report.append("  ✗ 第二阶段未收敛")
            analyzer.report.append("")
            analyzer.report.append("⚠️  警告: NEB计算部分收敛（第一阶段收敛，第二阶段未收敛）")
            analyzer.report.append("   建议:")
            analyzer.report.append("   - 增加第二阶段的最大步数")
            analyzer.report.append("   - 放宽收敛标准")
            analyzer.report.append("   - 检查反应路径是否合理")
    else:
        analyzer.report.append("  ✗ 第一阶段未收敛")
        analyzer.report.append("")
        analyzer.report.append("❌ NEB计算未收敛!")
        analyzer.report.append("   可能的原因:")
        analyzer.report.append("   - 初态和终态结构差异过大")
        analyzer.report.append("   - 插值路径不合理")
        analyzer.report.append("   - 收敛标准过严格")
        analyzer.report.append("   - 需要更多优化步数")
        analyzer.report.append("")
        analyzer.report.append("   建议:")
        analyzer.report.append("   - 增加第一阶段的最大步数")
        analyzer.report.append("   - 放宽收敛标准")
        analyzer.report.append("   - 检查初态和终态结构")
        analyzer.report.append("   - 使用更好的初始路径猜测")
    
    # 添加详细的收敛总结
    _add_convergence_summary(analyzer, convergence_info, initial_fmax, final_fmax_phase1, 
                           final_fmax_phase2 if conv1 else None)
    
    analyzer.report.append("")
    analyzer.report.append("="*80)
    analyzer.report.append(f"✓ NEB轨迹已保存: {trajectory_file}")
    analyzer.report.append("="*80)
    analyzer.report.append("")
    analyzer._write_report()  # 实时写入
    
    return trajectory_file, convergence_info


def _add_convergence_summary(analyzer, convergence_info, initial_fmax, final_fmax_phase1, final_fmax_phase2):
    """添加详细的收敛总结"""
    # 收敛总结
    analyzer.report.append("")
    analyzer.report.append("="*80)
    analyzer.report.append("详细收敛信息总结")
    analyzer.report.append("="*80)
    
    # 计算总的收敛状态
    total_converged = convergence_info['phase1']['converged'] and convergence_info['phase2']['converged']
    partially_converged = convergence_info['phase1']['converged'] and not convergence_info['phase2']['converged']
    not_converged = not convergence_info['phase1']['converged']
    
    # 收敛状态标题
    if total_converged:
        status_emoji = "🎉"
        status_text = "完全收敛"
        status_color = "✓"
    elif partially_converged:
        status_emoji = "⚠️ "
        status_text = "部分收敛"
        status_color = "⚠️"
    else:
        status_emoji = "❌"
        status_text = "未收敛"
        status_color = "✗"
    
    analyzer.report.append(f"{status_emoji} 总体收敛状态: {status_color} {status_text}")
    analyzer.report.append("")
    
    # 详细的阶段分析
    analyzer.report.append("阶段收敛详情:")
    analyzer.report.append("-" * 40)
    
    # 第一阶段
    phase1_status = "✓ 收敛" if convergence_info['phase1']['converged'] else "✗ 未收敛"
    analyzer.report.append(f"第一阶段 (预优化): {phase1_status}")
    analyzer.report.append(f"  执行步数: {convergence_info['phase1']['steps']}")
    analyzer.report.append(f"  初始最大受力: {initial_fmax:.4f} eV/Å")
    analyzer.report.append(f"  最终最大受力: {final_fmax_phase1:.4f} eV/Å")
    analyzer.report.append(f"  收敛标准: {analyzer.fmax + analyzer.delta_fmax_climb:.4f} eV/Å")
    
    # 第二阶段（如果第一阶段收敛）
    if convergence_info['phase1']['converged']:
        phase2_status = "✓ 收敛" if convergence_info['phase2']['converged'] else "✗ 未收敛"
        analyzer.report.append(f"第二阶段 (爬升镜像): {phase2_status}")
        analyzer.report.append(f"  执行步数: {convergence_info['phase2']['steps']}")
        analyzer.report.append(f"  初始最大受力: {final_fmax_phase1:.4f} eV/Å")
        if final_fmax_phase2 is not None:
            analyzer.report.append(f"  最终最大受力: {final_fmax_phase2:.4f} eV/Å")
        analyzer.report.append(f"  收敛标准: {analyzer.fmax:.4f} eV/Å")
    else:
        analyzer.report.append("第二阶段 (爬升镜像): 未执行 (第一阶段未收敛)")
    
    analyzer.report.append("")
    
    # 优化统计信息
    analyzer.report.append("优化统计:")
    analyzer.report.append("-" * 40)
    total_steps = convergence_info['phase1']['steps'] + convergence_info['phase2']['steps']
    analyzer.report.append(f"总优化步数: {total_steps}")
    analyzer.report.append(f"第一阶段步数: {convergence_info['phase1']['steps']} (目标: ≤{analyzer.max_steps_phase1})")
    if convergence_info['phase1']['converged']:
        analyzer.report.append(f"第二阶段步数: {convergence_info['phase2']['steps']} (目标: ≤{analyzer.max_steps_phase2})")
    
    # 受力变化详细统计
    analyzer.report.append("")
    analyzer.report.append("受力变化统计:")
    analyzer.report.append("-" * 40)
    
    force_reduction_phase1 = initial_fmax - final_fmax_phase1
    force_reduction_percent_phase1 = (force_reduction_phase1 / initial_fmax) * 100 if initial_fmax > 0 else 0
    
    analyzer.report.append(f"第一阶段受力减少:")
    analyzer.report.append(f"  绝对减少: {force_reduction_phase1:.4f} eV/Å")
    analyzer.report.append(f"  相对减少: {force_reduction_percent_phase1:.1f}%")
    
    if convergence_info['phase1']['converged'] and final_fmax_phase2 is not None:
        force_reduction_phase2 = final_fmax_phase1 - final_fmax_phase2
        force_reduction_percent_phase2 = (force_reduction_phase2 / final_fmax_phase1) * 100 if final_fmax_phase1 > 0 else 0
        
        analyzer.report.append(f"第二阶段受力减少:")
        analyzer.report.append(f"  绝对减少: {force_reduction_phase2:.4f} eV/Å")
        analyzer.report.append(f"  相对减少: {force_reduction_percent_phase2:.1f}%")
        
        total_force_reduction = initial_fmax - final_fmax_phase2
        total_force_reduction_percent = (total_force_reduction / initial_fmax) * 100 if initial_fmax > 0 else 0
        
        analyzer.report.append(f"总体受力减少:")
        analyzer.report.append(f"  绝对减少: {total_force_reduction:.4f} eV/Å")
        analyzer.report.append(f"  相对减少: {total_force_reduction_percent:.1f}%")
