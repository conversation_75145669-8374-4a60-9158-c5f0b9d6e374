"""
Calculator utilities for ML-NEB calculations
"""

from fairchem.core import FAIRChemCalculator
from fairchem.core.units.mlip_unit import load_predict_unit


def create_calculator(checkpoint_path="uma_sm.pt"):
    """
    Create a FAIRChem calculator instance
    
    Args:
        checkpoint_path (str): Path to the model checkpoint file
        
    Returns:
        FAIRChemCalculator: Configured calculator instance
        
    Raises:
        Exception: If calculator creation fails
    """
    try:
        predictor = load_predict_unit(checkpoint_path)
        calc = FAIRChemCalculator(predictor, task_name="oc20")
        return calc
    except Exception as e:
        raise Exception(f"Calculator creation failed: {e}")


def create_predictor(checkpoint_path="uma_sm.pt"):
    """
    Create a predictor unit for NEB calculations
    
    Args:
        checkpoint_path (str): Path to the model checkpoint file
        
    Returns:
        Predictor unit for FAIRChem calculations
        
    Raises:
        Exception: If predictor creation fails
    """
    try:
        predictor = load_predict_unit(checkpoint_path)
        return predictor
    except Exception as e:
        raise Exception(f"Predictor creation failed: {e}")
