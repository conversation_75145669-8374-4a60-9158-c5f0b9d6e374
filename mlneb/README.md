# ML-NEB: Machine Learning Enhanced Nudged Elastic Band Calculations

ML-NEB是一个用于执行机器学习增强的NEB（Nudged Elastic Band）计算的Python包，特别针对FAIRChem模型进行了优化。

## 功能特性

- **完整的NEB计算流程**: 从结构加载到能量分析的全自动化流程
- **机器学习势能**: 支持FAIRChem等先进的ML势能模型
- **轨迹处理**: 强大的轨迹文件合并、转换和分析工具
- **可视化**: 自动生成HTML格式的3D可视化文件
- **详细报告**: 生成包含收敛分析、能量统计等的详细报告
- **命令行接口**: 易于使用的CLI工具

## 包结构

```
mlneb/
├── __init__.py              # 主包入口
├── core/                    # 核心计算模块
│   ├── analyzer.py          # NEBAnalyzer主类
│   ├── calculator.py        # 计算器相关功能
│   └── neb_methods.py       # NEB计算方法
├── utils/                   # 工具模块
│   ├── trajectory.py        # 轨迹处理工具
│   └── io.py               # 输入输出工具
├── visualization/           # 可视化模块
│   └── html.py             # HTML可视化工具
├── cli/                    # 命令行接口
│   ├── neb_cli.py          # NEB命令行接口
│   ├── traj_cli.py         # 轨迹转换CLI
│   ├── combine_cli.py      # 合并CLI
│   └── __main__.py         # 主CLI入口
└── scripts/                # 独立脚本
    ├── neb.py              # NEB计算脚本
    ├── traj_to_html.py     # 轨迹转HTML脚本
    └── combine_trajectory.py # 合并脚本
```

## 安装和使用

### 基本使用

#### 1. 作为Python包使用

```python
from mlneb import NEBAnalyzer

# 创建NEB分析器
analyzer = NEBAnalyzer(
    initial_file="data/r11/ini.vasp",
    final_file="data/r11/fin.vasp", 
    output_dir="data/r11",
    reaction_label="r11"
)

# 运行完整分析
analyzer.run_full_analysis()
```

#### 2. 使用命令行接口

```bash
# 运行NEB计算
python -m mlneb.cli run r11

# 使用外部轨迹文件
python -m mlneb.cli run r11 --traj combined_trajectory.traj

# 转换轨迹文件为HTML
python -m mlneb.cli convert combined_trajectory.traj

# 合并VASP文件为轨迹
python -m mlneb.cli combine --input-dir totraj --output-file my_trajectory.traj
```

#### 3. 使用独立脚本

```bash
# NEB计算
python -m mlneb.scripts.neb r11

# 轨迹转换
python -m mlneb.scripts.traj_to_html combined_trajectory.traj

# 文件合并
python -m mlneb.scripts.combine_trajectory
```

### 高级功能

#### 轨迹处理

```python
from mlneb.utils.trajectory import combine_vasp_to_trajectory, analyze_trajectory

# 合并VASP文件
success = combine_vasp_to_trajectory(
    input_dir="totraj",
    output_file="my_trajectory.traj",
    file_range=(1, 9)
)

# 分析轨迹文件
info = analyze_trajectory("my_trajectory.traj")
print(f"帧数: {info['frame_count']}")
print(f"原子数: {info['atom_count']}")
```

#### 可视化

```python
from mlneb.visualization.html import convert_trajectory_to_html

# 转换轨迹为HTML可视化
success = convert_trajectory_to_html(
    "my_trajectory.traj",
    "my_visualization.html"
)
```

## 输出文件

NEB计算会生成以下文件：

- `neb_analysis_report_{reaction_label}.txt`: 详细分析报告
- `data/{reaction_label}/interpolated_frames.html`: 插值帧可视化
- `data/{reaction_label}/{reaction_label}.traj`: NEB轨迹文件
- `data/{reaction_label}/reaction_coordinate.png`: 反应坐标图
- `data/{reaction_label}/{reaction_label}_neb.html`: 优化后NEB可视化

## 配置选项

### NEB计算参数

- `fmax`: 力收敛标准 (默认: 0.05 eV/Å)
- `delta_fmax_climb`: 攀爬图像力阈值 (默认: 0.4 eV/Å)
- `k`: 弹簧常数 (默认: 1.0)
- `num_frames`: 插值帧数 (默认: 10)
- `max_steps_phase1`: 第一阶段最大步数 (默认: 200)
- `max_steps_phase2`: 第二阶段最大步数 (默认: 300)

### 模型配置

- `checkpoint_path`: 模型检查点路径 (默认: "uma_sm.pt")
- `cpu`: 是否使用CPU计算 (默认: False)

## 示例

### 基本NEB计算

```python
from mlneb import NEBAnalyzer

analyzer = NEBAnalyzer(
    initial_file="data/r11/ini.vasp",
    final_file="data/r11/fin.vasp",
    output_dir="data/r11", 
    reaction_label="r11",
    fmax=0.03,  # 更严格的收敛标准
    num_frames=15  # 更多插值帧
)

analyzer.run_full_analysis()
```

### 使用外部轨迹

```python
analyzer = NEBAnalyzer(
    initial_file="data/r11/ini.vasp",
    final_file="data/r11/fin.vasp",
    output_dir="data/r11",
    reaction_label="r11",
    trajectory_file="my_custom_path.traj"
)

analyzer.run_full_analysis()
```

## 故障排除

1. **导入错误**: 确保所有依赖包已正确安装
2. **模型文件**: 确保`uma_sm.pt`或指定的模型文件存在
3. **内存不足**: 对于大系统，考虑使用CPU模式或减少帧数
4. **收敛问题**: 调整收敛标准或增加最大步数

## 依赖项

- ASE (Atomic Simulation Environment)
- FAIRChem
- PyMatGen
- NumPy
- Matplotlib
- x3dase

## 许可证

本项目采用MIT许可证。
