#!/usr/bin/env python3
"""
VASP文件合并命令行接口
"""

import argparse
from ..utils.trajectory import combine_vasp_to_trajectory


def create_combine_parser():
    """创建合并命令行参数解析器"""
    parser = argparse.ArgumentParser(description="VASP文件轨迹合并工具")
    parser.add_argument("--input-dir", default="totraj", 
                       help="输入文件目录 (默认: totraj)")
    parser.add_argument("--output-file", default="combined_trajectory.traj",
                       help="输出轨迹文件路径 (默认: combined_trajectory.traj)")
    parser.add_argument("--start", type=int, default=1,
                       help="起始文件编号 (默认: 1)")
    parser.add_argument("--end", type=int, default=9,
                       help="结束文件编号 (默认: 9)")
    parser.add_argument("--quiet", "-q", action="store_true",
                       help="静默模式，减少输出信息")
    
    return parser


def run_combine_operation(args):
    """运行合并操作"""
    try:
        print("="*60)
        print("VASP文件轨迹合并工具")
        print("="*60)
        
        print(f"输入目录: {args.input_dir}")
        print(f"输出文件: {args.output_file}")
        print(f"文件范围: {args.start} 到 {args.end-1}")
        print("-" * 60)
        
        success = combine_vasp_to_trajectory(
            input_dir=args.input_dir,
            output_file=args.output_file,
            file_range=(args.start, args.end)
        )
        
        if success:
            print("\n✓ 轨迹文件合并完成!")
            print("现在可以使用以下命令测试新的轨迹文件:")
            print(f"mlneb-run test --traj {args.output_file}")
            return True
        else:
            print("\n✗ 轨迹文件合并失败!")
            return False
            
    except Exception as e:
        print(f"\n错误: {e}")
        return False


def main():
    """主函数"""
    parser = create_combine_parser()
    args = parser.parse_args()
    
    success = run_combine_operation(args)
    
    if not success:
        exit(1)


if __name__ == "__main__":
    main()
