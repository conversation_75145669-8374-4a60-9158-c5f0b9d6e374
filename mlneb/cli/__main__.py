#!/usr/bin/env python3
"""
ML-NEB命令行主入口点
"""

import sys
import argparse
from .neb_cli import create_neb_parser, run_neb_analysis
from .traj_cli import create_traj_parser, run_traj_conversion
from .combine_cli import create_combine_parser, run_combine_operation


def create_main_parser():
    """创建主命令行解析器"""
    parser = argparse.ArgumentParser(
        prog="mlneb",
        description="ML-NEB: Machine Learning Enhanced Nudged Elastic Band Calculations"
    )

    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # NEB计算子命令 - 直接使用原始解析器
    neb_parser = subparsers.add_parser("run", help="运行NEB计算",
                                      parents=[create_neb_parser()],
                                      conflict_handler='resolve')

    # 轨迹转换子命令
    traj_parser = subparsers.add_parser("convert", help="转换轨迹文件为HTML",
                                       parents=[create_traj_parser()],
                                       conflict_handler='resolve')

    # 合并轨迹子命令
    combine_parser = subparsers.add_parser("combine", help="合并VASP文件为轨迹",
                                          parents=[create_combine_parser()],
                                          conflict_handler='resolve')

    return parser


def main():
    """主函数"""
    parser = create_main_parser()
    
    if len(sys.argv) == 1:
        # 如果没有参数，显示帮助
        parser.print_help()
        return
    
    args = parser.parse_args()
    
    if args.command == "run":
        success = run_neb_analysis(args)
    elif args.command == "convert":
        success = run_traj_conversion(args)
    elif args.command == "combine":
        success = run_combine_operation(args)
    else:
        parser.print_help()
        return
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
