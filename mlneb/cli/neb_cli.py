#!/usr/bin/env python3
"""
NEB计算命令行接口
"""

import os
import argparse
from ..core.analyzer import NEBAnalyzer


def create_neb_parser():
    """创建NEB命令行参数解析器"""
    parser = argparse.ArgumentParser(description="NEB (Nudged Elastic Band) 计算脚本")
    parser.add_argument("reaction_label", nargs="?", default="r11", 
                       help="反应标签 (默认: r11). 脚本会自动查找 data/{reaction_label}/ini.vasp 和 data/{reaction_label}/fin.vasp")
    parser.add_argument("--final-file", 
                       help="指定终态文件路径 (默认: data/{reaction_label}/fin.vasp)")
    parser.add_argument("--trajectory-file", "--traj", 
                       help="指定外部轨迹文件路径. 如果提供，将使用该文件中的帧而非内置插值算法")
    
    # NEB计算参数
    parser.add_argument("--fmax", type=float, default=0.05,
                       help="力收敛标准 (eV/Å, 默认: 0.05)")
    parser.add_argument("--delta-fmax-climb", type=float, default=0.4,
                       help="攀爬图像的力阈值 (eV/Å, 默认: 0.4)")
    parser.add_argument("--spring-constant", "--k", type=float, default=1.0,
                       help="弹簧常数 (默认: 1.0)")
    parser.add_argument("--num-frames", type=int, default=10,
                       help="插值帧数 (默认: 10)")
    parser.add_argument("--cpu", action="store_true", default=False,
                       help="使用CPU而非GPU进行计算")
    parser.add_argument("--checkpoint-path", default="uma_sm.pt",
                       help="模型检查点文件路径 (默认: uma_sm.pt)")
    parser.add_argument("--max-steps-phase1", type=int, default=200,
                       help="第一阶段优化最大步数 (默认: 200)")
    parser.add_argument("--max-steps-phase2", type=int, default=300,
                       help="第二阶段优化最大步数 (默认: 300)")
    parser.add_argument("--random-seed", type=int, default=None,
                       help="随机种子，用于确保结果可重现 (默认: 随机生成)")
    
    return parser


def run_neb_analysis(args):
    """运行NEB分析"""
    reaction_label = args.reaction_label
    
    try:
        # 设置文件路径
        output_dir = f'data/{reaction_label}'
        initial_file = f'data/{reaction_label}/ini.vasp'
        
        # 检查是否指定了自定义终态文件
        if args.final_file:
            final_file = args.final_file
        else:
            # 使用 fin.vasp
            changed_final_file = f'data/{reaction_label}/fin.vasp'
            default_final_file = f'data/{reaction_label}/fin.vasp'
            
            if os.path.exists(changed_final_file):
                final_file = changed_final_file
                print(f"使用修改后的终态文件: {changed_final_file}")
            elif os.path.exists(default_final_file):
                final_file = default_final_file
                print(f"使用默认终态文件: {default_final_file}")
            else:
                raise FileNotFoundError(f"未找到终态文件: {changed_final_file} 或 {default_final_file}")
        
        # 检查文件是否存在
        if not os.path.exists(initial_file):
            raise FileNotFoundError(f"初态文件不存在: {initial_file}")
        if not os.path.exists(final_file):
            raise FileNotFoundError(f"终态文件不存在: {final_file}")
        
        print(f"开始分析反应: {reaction_label}")
        print(f"初态文件: {initial_file}")
        print(f"终态文件: {final_file}")
        print(f"输出目录: {output_dir}")
        if args.trajectory_file:
            print(f"外部轨迹文件: {args.trajectory_file}")
        
        # 打印NEB参数
        print(f"NEB参数:")
        print(f"  力收敛标准 (fmax): {args.fmax} eV/Å")
        print(f"  攀爬图像力阈值: {args.delta_fmax_climb} eV/Å")
        print(f"  弹簧常数: {args.spring_constant}")
        print(f"  插值帧数: {args.num_frames}")
        print(f"  计算设备: {'CPU' if args.cpu else 'GPU'}")
        print(f"  模型检查点: {args.checkpoint_path}")
        print(f"  第一阶段最大步数: {args.max_steps_phase1}")
        print(f"  第二阶段最大步数: {args.max_steps_phase2}")
        if args.random_seed is not None:
            print(f"  随机种子: {args.random_seed} (用户指定)")
        else:
            print(f"  随机种子: 将自动生成")
        print("-" * 50)
        
        # 初始化分析器
        analyzer = NEBAnalyzer(
            initial_file, final_file, output_dir, reaction_label, 
            trajectory_file=args.trajectory_file,
            fmax=args.fmax,
            delta_fmax_climb=args.delta_fmax_climb,
            k=args.spring_constant,
            num_frames=args.num_frames,
            cpu=args.cpu,
            checkpoint_path=args.checkpoint_path,
            max_steps_phase1=args.max_steps_phase1,
            max_steps_phase2=args.max_steps_phase2,
            random_seed=args.random_seed
        )
        
        # 运行完整的分析工作流程
        analyzer.run_full_analysis()
        
        print("\n" + "="*60)
        print("NEB分析完成!")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"\n错误: {e}")
        print("\n可能的解决方案:")
        print("1. 检查输入文件路径是否正确")
        print("2. 确保模型检查点文件存在")
        print("3. 检查计算资源是否充足")
        print("4. 查看详细错误信息进行调试")
        return False


def main():
    """主函数"""
    parser = create_neb_parser()
    args = parser.parse_args()
    
    print("="*60)
    print("ML-NEB: Machine Learning Enhanced NEB Calculations")
    print("="*60)
    
    success = run_neb_analysis(args)
    
    if not success:
        exit(1)


if __name__ == "__main__":
    main()
