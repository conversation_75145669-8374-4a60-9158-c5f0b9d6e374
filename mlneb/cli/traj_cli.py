#!/usr/bin/env python3
"""
轨迹转换命令行接口
"""

import argparse
from ..visualization.html import convert_trajectory_to_html


def create_traj_parser():
    """创建轨迹转换命令行参数解析器"""
    parser = argparse.ArgumentParser(description="轨迹文件HTML可视化转换工具")
    parser.add_argument("traj_file", help="输入轨迹文件路径")
    parser.add_argument("output_html", nargs="?", default=None, 
                       help="输出HTML文件路径 (可选，默认基于输入文件名)")
    parser.add_argument("--quiet", "-q", action="store_true", 
                       help="静默模式，减少输出信息")
    
    return parser


def run_traj_conversion(args):
    """运行轨迹转换"""
    try:
        print("="*60)
        print("轨迹文件HTML可视化转换工具")
        print("="*60)
        
        print(f"输入轨迹文件: {args.traj_file}")
        if args.output_html:
            print(f"输出HTML文件: {args.output_html}")
        else:
            import os
            base_name = os.path.splitext(args.traj_file)[0]
            print(f"输出HTML文件: {base_name}.html (自动命名)")
        
        print("-" * 60)
        
        success = convert_trajectory_to_html(args.traj_file, args.output_html)
        
        if success:
            print(f"\n✓ 转换完成!")
            return True
        else:
            print(f"\n✗ 转换失败!")
            return False
            
    except Exception as e:
        print(f"\n错误: {e}")
        return False


def main():
    """主函数"""
    parser = create_traj_parser()
    args = parser.parse_args()
    
    success = run_traj_conversion(args)
    
    if not success:
        exit(1)


if __name__ == "__main__":
    main()
