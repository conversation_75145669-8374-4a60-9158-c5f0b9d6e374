"""
HTML visualization utilities for ML-NEB package
"""

import os
from typing import List, Optional
from ase import Atoms
from ase.io import read
from x3dase.x3d import X3D
from collections import Counter


def convert_trajectory_to_html(traj_file: str, 
                              output_html: Optional[str] = None) -> bool:
    """
    Convert trajectory file to HTML visualization
    
    Args:
        traj_file (str): Input trajectory file path
        output_html (str, optional): Output HTML file path. If None, 
                                   auto-generated based on input filename
        
    Returns:
        bool: True if successful, False otherwise
    """
    
    # Check input file exists
    if not os.path.exists(traj_file):
        print(f"错误: 轨迹文件不存在: {traj_file}")
        return False
    
    # Set output filename if not provided
    if output_html is None:
        base_name = os.path.splitext(traj_file)[0]
        output_html = f"{base_name}.html"
    
    print(f"正在读取轨迹文件: {traj_file}")
    
    try:
        # Read trajectory file
        structures = read(traj_file, ":")
        print(f"✓ 成功读取 {len(structures)} 帧")
        print(f"✓ 每帧包含 {len(structures[0])} 个原子")
        
        # Display element composition
        symbols = structures[0].get_chemical_symbols()
        element_count = Counter(symbols)
        print(f"✓ 元素组成: {dict(element_count)}")
        
    except Exception as e:
        print(f"错误: 无法读取轨迹文件: {e}")
        return False
    
    print(f"\n正在生成HTML可视化文件: {output_html}")
    
    try:
        # Create X3D visualization object
        x3d_viz = X3D(structures)
        
        # Write HTML file
        x3d_viz.write(output_html)
        
        print(f"✓ 成功生成HTML文件: {output_html}")
        print(f"✓ 文件大小: {os.path.getsize(output_html) / 1024:.1f} KB")
        
        # Provide usage suggestions
        _print_usage_tips(output_html)
        
        return True
        
    except Exception as e:
        print(f"错误: 无法生成HTML文件: {e}")
        return False


def create_neb_visualization(structures: List[Atoms], 
                           output_path: str,
                           title: str = "NEB Pathway") -> str:
    """
    Create HTML visualization for NEB pathway
    
    Args:
        structures (List[Atoms]): List of atomic structures
        output_path (str): Output HTML file path
        title (str): Title for the visualization
        
    Returns:
        str: Path to generated HTML file
        
    Raises:
        Exception: If visualization creation fails
    """
    try:
        x3d = X3D(structures)
        x3d.write(output_path)
        return output_path
    except Exception as e:
        raise Exception(f"Failed to create NEB visualization: {e}")


def create_interpolated_frames_visualization(structures: List[Atoms], 
                                           output_path: str) -> str:
    """
    Create HTML visualization for interpolated frames
    
    Args:
        structures (List[Atoms]): List of interpolated structures
        output_path (str): Output HTML file path
        
    Returns:
        str: Path to generated HTML file
        
    Raises:
        Exception: If visualization creation fails
    """
    try:
        x3d_interpolated = X3D(structures)
        x3d_interpolated.write(output_path)
        return output_path
    except Exception as e:
        raise Exception(f"Failed to create interpolated frames visualization: {e}")


def _print_usage_tips(html_file: str) -> None:
    """
    Print usage tips for the generated HTML file
    
    Args:
        html_file (str): Path to the HTML file
    """
    print(f"\n使用建议:")
    print(f"1. 在浏览器中打开: {os.path.abspath(html_file)}")
    print(f"2. 使用VS Code预览: 右键 → 'Open with Live Server' 或 'Preview'")
    print(f"3. 支持的功能:")
    print(f"   - 3D旋转和缩放")
    print(f"   - 播放轨迹动画")
    print(f"   - 调整播放速度")
    print(f"   - 暂停/继续播放")


def validate_html_output(html_file: str) -> bool:
    """
    Validate that HTML file was created successfully
    
    Args:
        html_file (str): Path to HTML file to validate
        
    Returns:
        bool: True if file exists and has reasonable size
    """
    if not os.path.exists(html_file):
        return False
    
    # Check file has reasonable size (> 1KB)
    size_kb = os.path.getsize(html_file) / 1024
    return size_kb > 1.0
