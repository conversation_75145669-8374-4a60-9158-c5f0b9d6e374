[{"No.": "R9", "Reaction": "*CO + *O -> *CO2", "Ea": 0.65, "Type": "polymerization", "reactant1": 5, "reactant2": 0, "product": 86}, {"No.": "R10", "Reaction": "*CO + *OH -> *COOH", "Ea": 0.56, "Type": "polymerization", "reactant1": 5, "reactant2": 2, "product": 84}, {"No.": "R11", "Reaction": "*COOH -> *CO2 + *H", "Ea": 1.23, "Type": "dissociation", "reactant": 84, "product1": 86, "product2": 1}, {"No.": "R12", "Reaction": "*COOH + *OH -> *CO2 + *H2O", "Ea": 0, "Type": "transfer", "reactant1": 84, "reactant2": 2, "product1": 86, "product2": 3}, {"No.": "R13", "Reaction": "*COOH + *H -> *HCOOH", "Ea": 0.73, "Type": "polymerization", "reactant1": 84, "reactant2": 1, "product": 87}, {"No.": "R14", "Reaction": "*H2O -> *OH + *H", "Ea": 1.39, "Type": "dissociation", "reactant": 3, "product1": 2, "product2": 1}, {"No.": "R15", "Reaction": "*OH -> *O + *H", "Ea": 1.68, "Type": "dissociation", "reactant": 2, "product1": 0, "product2": 1}, {"No.": "R16", "Reaction": "2 *OH -> *H2O + *O", "Ea": 0.61, "Type": "transfer", "reactant1": 2, "reactant2": 2, "product1": 3, "product2": 0}, {"No.": "R17", "Reaction": "*CO2 + *H -> *HCOO", "Ea": 0.87, "Type": "polymerization", "reactant1": 86, "reactant2": 1, "product": 83}, {"No.": "R18", "Reaction": "*HCOO + *H -> *H2CO2", "Ea": 1.59, "Type": "polymerization", "reactant1": 83, "reactant2": 1, "product": 90}, {"No.": "R19", "Reaction": "*HCOO + *H -> *HCOOH", "Ea": 0.91, "Type": "polymerization", "reactant1": 83, "reactant2": 1, "product": 87}, {"No.": "R20", "Reaction": "*HCOOH + *H -> *CH3O2", "Ea": 1.04, "Type": "polymerization", "reactant1": 87, "reactant2": 1, "product": 91}, {"No.": "R21", "Reaction": "*H2CO2 + *H -> *CH3O2", "Ea": 0.74, "Type": "polymerization", "reactant1": 90, "reactant2": 1, "product": 91}, {"No.": "R22", "Reaction": "*H2CO2 -> *CH3O + *O", "Ea": 0.91, "Type": "dissociation", "reactant": 90, "product1": 13, "product2": 0}, {"No.": "R23", "Reaction": "*CH3O2 -> *CH3O + *OH", "Ea": 0.74, "Type": "dissociation", "reactant": 91, "product1": 13, "product2": 2}, {"No.": "R24", "Reaction": "*CH2O + *H -> *CH3O", "Ea": 0.24, "Type": "polymerization", "reactant1": 10, "reactant2": 1, "product": 13}, {"No.": "R25", "Reaction": "*CH3O + *H -> *CH3OH", "Ea": 1.17, "Type": "polymerization", "reactant1": 13, "reactant2": 1, "product": 16}, {"No.": "R26", "Reaction": "*CO + *H -> *HCO", "Ea": 0.99, "Type": "polymerization", "reactant1": 5, "reactant2": 1, "product": 92}, {"No.": "R27", "Reaction": "*CO + *H -> *COH", "Ea": 2.26, "Type": "polymerization", "reactant1": 5, "reactant2": 1, "product": 8}, {"No.": "R28", "Reaction": "*HCOO -> *HCO + *O", "Ea": 2.36, "Type": "dissociation", "reactant": 83, "product1": 92, "product2": 0}, {"No.": "R29", "Reaction": "*HCO + *H -> *HCOH", "Ea": 0.91, "Type": "polymerization", "reactant1": 92, "reactant2": 1, "product": 93}, {"No.": "R30", "Reaction": "*HCO + *H -> *CH2O", "Ea": 0.47, "Type": "polymerization", "reactant1": 92, "reactant2": 1, "product": 10}, {"No.": "R31", "Reaction": "*CH2O + *H -> *CH2OH", "Ea": 0.82, "Type": "polymerization", "reactant1": 10, "reactant2": 1, "product": 14}, {"No.": "R32", "Reaction": "*HCOH + *H -> *CH2OH", "Ea": 0.47, "Type": "polymerization", "reactant1": 93, "reactant2": 1, "product": 14}, {"No.": "R33", "Reaction": "*CH2OH + *H -> *CH3OH", "Ea": 0.51, "Type": "polymerization", "reactant1": 14, "reactant2": 1, "product": 16}, {"No.": "R34", "Reaction": "*HCOOH -> *HCO + *OH", "Ea": 1.63, "Type": "dissociation", "reactant": 87, "product1": 92, "product2": 2}, {"No.": "R35", "Reaction": "*HCOOH -> *HCOO + *O", "Ea": 2.5, "Type": "dissociation", "reactant": 87, "product1": 83, "product2": 0}, {"No.": "R36", "Reaction": "*CH3O2 -> *CH2OH + *O", "Ea": 2.01, "Type": "dissociation", "reactant": 91, "product1": 14, "product2": 0}, {"No.": "R37", "Reaction": "*CO2 + *O -> *CO3", "Ea": 0.34, "Type": "polymerization", "reactant1": 86, "reactant2": 0, "product": 89}, {"No.": "R38", "Reaction": "*CO3 + *H -> *HCO3", "Ea": 1.0, "Type": "polymerization", "reactant1": 89, "reactant2": 1, "product": 88}, {"No.": "R39", "Reaction": "*O + *HCO -> *OH + *CO", "Ea": 0, "Type": "transfer", "reactant1": 0, "reactant2": 92, "product1": 2, "product2": 5}, {"No.": "R40", "Reaction": "*OH + *HCO -> *H2O + *CO", "Ea": 0.3, "Type": "transfer", "reactant1": 2, "reactant2": 92, "product1": 3, "product2": 5}, {"No.": "R41", "Reaction": "*HCOO + *HCO -> *HCOOH + *CO", "Ea": 0.6, "Type": "transfer", "reactant1": 83, "reactant2": 92, "product1": 87, "product2": 5}, {"No.": "R42", "Reaction": "*HCOO + *HCO -> *H2CO2 + *CO", "Ea": 0.8, "Type": "transfer", "reactant1": 83, "reactant2": 92, "product1": 90, "product2": 5}, {"No.": "R43", "Reaction": "*HCOOH + *HCO -> *CH3O2 + *CO", "Ea": 0.42, "Type": "transfer", "reactant1": 87, "reactant2": 92, "product1": 91, "product2": 5}, {"No.": "R44", "Reaction": "*CH3O + *HCO -> *CH3O2 + *CO", "Ea": 0, "Type": "transfer", "reactant1": 13, "reactant2": 92, "product1": 91, "product2": 5}, {"No.": "R45", "Reaction": "*CH3O + *HCO -> *CH3OH + *CO", "Ea": 0.38, "Type": "transfer", "reactant1": 13, "reactant2": 92, "product1": 16, "product2": 5}]