<html>
<head>
<title>ASE atomic visualization</title>
<link rel="stylesheet" type="text/css"
 href="https://www.x3dom.org/x3dom/release/x3dom.css">
</link>
<script type="text/javascript"
 src="https://www.x3dom.org/x3dom/release/x3dom.js">
</script>
<style>
* {
    box-sizing: border-box;
  }
 
/* Create two unequal columns that floats next to each other */
.column {
  float: left;
  padding: 1px;
}

.left {
  width: 20%;
}

.right {
  width: 80%;
}

/* Clear floats after the columns */
.row:after {
  content: "";
  display: table;
  clear: both;
}

#x3dase{
    top:0;
    width: 80%;
    height: 80%;
    border:2px solid darkorange;        
}

#sidebar{
    top:0;
    border:2px solid darkorange;        
}


/* Sidebar component containers */
.ui-widget-header
{
  background-color: lightblue;
  font-size: 12px;

}
.sidebarComponent
{
    padding:2px 2px 2px 2px;
    font-size: medium;
}

.button {
  background-color: #4CAF50; /* Green */
  border: 1px solid green;
  color: white;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 10px;
  cursor: pointer;
  /* float: left; */
}

</style></head>
<body>
<div class = "column left", id = "sidebar">

    <div class="ui-widget-header">Model</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="spacefilling('4aa8e020')">Ball</button>
            <button type="button" class = "button" onclick="ballstick('4aa8e020')">Ball-and-stick</button>
            <button type="button" class = "button" onclick="polyhedra('4aa8e020')">Polyhedra</button>
        </form>
    </div>
    <div class="ui-widget-header">Label</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="none('4aa8e020')">None</button>
            <button type="button" class = "button" onclick="element('4aa8e020')"> Element</button>
            <button type="button" class = "button" onclick="index('4aa8e020')">Index</button>
        </form>
    </div>

    <div class="ui-widget-header">Camera</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="document.getElementById('camera_ortho_4aa8e020').setAttribute('set_bind','true');">Orthographic</button>
            <button type="button" class = "button" onclick="document.getElementById('camera_persp_4aa8e020').setAttribute('set_bind','true');">Perspective</button>
        </form>
    </div>

    <div class="ui-widget-header">View</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="set_viewpoint('4aa8e020', 'top_pos', 'top_ori')">Top</button>
            <button type="button" class = "button" onclick="set_viewpoint('4aa8e020', 'front_pos', 'front_ori')">Front</button>
            <button type="button" class = "button" onclick="set_viewpoint('4aa8e020', 'right_pos', 'right_ori')">Right</button>
        </form>
    </div>

    <div class="ui-widget-header">Measurement</div>
    <div class="sidebarComponent">
        <form style="text-align:left; font-size: 12px;">
            <table style="font-size:1.0em;">
                <td id="lastonMouseoverObject_kind_4aa8e020">-</td> <td id="lastonMouseoverObject_index_4aa8e020">-</td> 
                <td id="position_4aa8e020">-</td></tr>
            </table>
            <p id="measure_4aa8e020"></p>
            <p id="error_4aa8e020"></p>
        </form>
    </div>

</div>

<script>
    document.onkeyup = function(e) {
      var x = event.which || event.keyCode;
      var label = 0;
        if (x == 49) {
            set_viewpoint('4aa8e020', 'top_pos', 'top_ori');
        } else if (x == 50) {
            set_viewpoint('4aa8e020', 'front_pos', 'front_ori');
        } else if (x == 51) {
            set_viewpoint('4aa8e020', 'right_pos', 'right_ori');
        } else if (x == 83) {
          spacefilling('4aa8e020');
        } else if (x == 66) {
          ballstick('4aa8e020');
        } else if (x == 80) {
          polyhedra('4aa8e020');
        } else if (x == 52) {
            element('4aa8e020');
        } else if (x == 53) {
            index('4aa8e020');
        } else if (x == 54) {
            none('4aa8e020');
        }
      };
    </script>
 <div class = "column right" > 
   <X3D id = "x3dase" PrimitiveQuality = "high" > 
     <Scene > 
       <Transform id = "t_camera_persp_4aa8e020" rotation = "0 0 0 0" > 
         <Viewpoint id = "camera_persp_4aa8e020" position = "2.4967547552342126 4.324238904897057 58.14534023565058" centerOfRotation = "2.4967547552342126 4.324238904897057 19.143910284531874" orientation = "0 0 0 0" description = "camera" > 
         </Viewpoint> 
       </Transform> 
       <Transform id = "t_camera_ortho_4aa8e020" rotation = "0 0 0 0" > 
         <OrthoViewpoint id = "camera_ortho_4aa8e020" position = "2.4967547552342126 4.324238904897057 58.14534023565058" centerOfRotation = "2.4967547552342126 4.324238904897057 19.143910284531874" orientation = "0 0 0 0" fieldOfView = "-13.000476650372901 -13.000476650372901 13.000476650372901 13.000476650372901" description = "camera" > 
         </OrthoViewpoint> 
       </Transform> 
       <Group onclick = "handleGroupClick(event, '4aa8e020')" > 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_C" id = "as_C_4aa8e020" > 
             <Appearance DEF = "app_C" > 
               <Material name = "am_4aa8e020" diffuseColor = "0.565 0.565 0.565" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_C" radius = "0.76" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_98_4aa8e020" uuid = "4aa8e020" id = "at_98_4aa8e020" radius = "0.76" name = "at_4aa8e020" translation = "3.13 5.39 27.18" scale = "1 1 1" > 
           <Shape kind = "C" index = "98" uuid = "4aa8e020" > 
             <Appearance USE = "app_C" > 
             </Appearance> 
             <Sphere USE = "asp_C" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_H" id = "as_H_4aa8e020" > 
             <Appearance DEF = "app_H" > 
               <Material name = "am_4aa8e020" diffuseColor = "1.0 1.0 1.0" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_H" radius = "0.31" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_99_4aa8e020" uuid = "4aa8e020" id = "at_99_4aa8e020" radius = "0.31" name = "at_4aa8e020" translation = "4.13 7.08 27.13" scale = "1 1 1" > 
           <Shape kind = "H" index = "99" uuid = "4aa8e020" > 
             <Appearance USE = "app_H" > 
             </Appearance> 
             <Sphere USE = "asp_H" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_O" id = "as_O_4aa8e020" > 
             <Appearance DEF = "app_O" > 
               <Material name = "am_4aa8e020" diffuseColor = "1.0 0.051 0.051" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_O" radius = "0.66" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_96_4aa8e020" uuid = "4aa8e020" id = "at_96_4aa8e020" radius = "0.66" name = "at_4aa8e020" translation = "2.62 4.54 26.44" scale = "1 1 1" > 
           <Shape kind = "O" index = "96" uuid = "4aa8e020" > 
             <Appearance USE = "app_O" > 
             </Appearance> 
             <Sphere USE = "asp_O" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_97_4aa8e020" uuid = "4aa8e020" id = "at_97_4aa8e020" radius = "0.66" name = "at_4aa8e020" translation = "3.75 6.45 26.48" scale = "1 1 1" > 
           <Shape kind = "O" index = "97" uuid = "4aa8e020" > 
             <Appearance USE = "app_O" > 
             </Appearance> 
             <Sphere USE = "asp_O" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_Cu" id = "as_Cu_4aa8e020" > 
             <Appearance DEF = "app_Cu" > 
               <Material name = "am_4aa8e020" diffuseColor = "0.784 0.502 0.2" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_Cu" radius = "1.32" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_0_4aa8e020" uuid = "4aa8e020" id = "at_0_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-1.3 2.25 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "0" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_1_4aa8e020" uuid = "4aa8e020" id = "at_1_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-0.0 1.5 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "1" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_2_4aa8e020" uuid = "4aa8e020" id = "at_2_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "1.3 0.75 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "2" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_3_4aa8e020" uuid = "4aa8e020" id = "at_3_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "1.29 2.23 24.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "3" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_4_4aa8e020" uuid = "4aa8e020" id = "at_4_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-0.0 1.5 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "4" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_5_4aa8e020" uuid = "4aa8e020" id = "at_5_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "1.3 0.75 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "5" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_6_4aa8e020" uuid = "4aa8e020" id = "at_6_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-2.6 4.5 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "6" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_7_4aa8e020" uuid = "4aa8e020" id = "at_7_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-1.3 3.75 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "7" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_8_4aa8e020" uuid = "4aa8e020" id = "at_8_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "0.0 3.0 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "8" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_9_4aa8e020" uuid = "4aa8e020" id = "at_9_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-0.03 4.5 24.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "9" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_10_4aa8e020" uuid = "4aa8e020" id = "at_10_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-1.3 3.75 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "10" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_11_4aa8e020" uuid = "4aa8e020" id = "at_11_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "0.0 3.0 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "11" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_12_4aa8e020" uuid = "4aa8e020" id = "at_12_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-3.9 6.76 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "12" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_13_4aa8e020" uuid = "4aa8e020" id = "at_13_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-2.6 6.0 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "13" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_14_4aa8e020" uuid = "4aa8e020" id = "at_14_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-1.3 5.25 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "14" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_15_4aa8e020" uuid = "4aa8e020" id = "at_15_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-1.3 6.76 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "15" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_16_4aa8e020" uuid = "4aa8e020" id = "at_16_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-2.6 6.0 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "16" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_17_4aa8e020" uuid = "4aa8e020" id = "at_17_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-1.3 5.25 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "17" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_18_4aa8e020" uuid = "4aa8e020" id = "at_18_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "0.0 0.0 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "18" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_19_4aa8e020" uuid = "4aa8e020" id = "at_19_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-3.9 8.26 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "19" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_20_4aa8e020" uuid = "4aa8e020" id = "at_20_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-2.6 7.51 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "20" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_21_4aa8e020" uuid = "4aa8e020" id = "at_21_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-2.6 9.0 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "21" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_22_4aa8e020" uuid = "4aa8e020" id = "at_22_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-3.9 8.26 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "22" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_23_4aa8e020" uuid = "4aa8e020" id = "at_23_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-2.6 7.51 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "23" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_24_4aa8e020" uuid = "4aa8e020" id = "at_24_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "1.3 2.25 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "24" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_25_4aa8e020" uuid = "4aa8e020" id = "at_25_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "2.6 1.5 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "25" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_26_4aa8e020" uuid = "4aa8e020" id = "at_26_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "3.9 0.75 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "26" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_27_4aa8e020" uuid = "4aa8e020" id = "at_27_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "3.91 2.23 24.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "27" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_28_4aa8e020" uuid = "4aa8e020" id = "at_28_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "2.6 1.5 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "28" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_29_4aa8e020" uuid = "4aa8e020" id = "at_29_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "3.9 0.75 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "29" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_30_4aa8e020" uuid = "4aa8e020" id = "at_30_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "0.0 4.5 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "30" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_31_4aa8e020" uuid = "4aa8e020" id = "at_31_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "1.3 3.75 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "31" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_32_4aa8e020" uuid = "4aa8e020" id = "at_32_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "2.6 3.0 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "32" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_33_4aa8e020" uuid = "4aa8e020" id = "at_33_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "2.59 4.49 24.37" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "33" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_34_4aa8e020" uuid = "4aa8e020" id = "at_34_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "1.3 3.75 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "34" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_35_4aa8e020" uuid = "4aa8e020" id = "at_35_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "2.6 3.0 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "35" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_36_4aa8e020" uuid = "4aa8e020" id = "at_36_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-1.3 6.76 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "36" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_37_4aa8e020" uuid = "4aa8e020" id = "at_37_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-0.0 6.0 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "37" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_38_4aa8e020" uuid = "4aa8e020" id = "at_38_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "1.3 5.25 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "38" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_39_4aa8e020" uuid = "4aa8e020" id = "at_39_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "1.27 6.77 24.28" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "39" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_40_4aa8e020" uuid = "4aa8e020" id = "at_40_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-0.0 6.0 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "40" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_41_4aa8e020" uuid = "4aa8e020" id = "at_41_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "1.3 5.25 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "41" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_42_4aa8e020" uuid = "4aa8e020" id = "at_42_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "2.6 0.0 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "42" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_43_4aa8e020" uuid = "4aa8e020" id = "at_43_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-1.3 8.26 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "43" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_44_4aa8e020" uuid = "4aa8e020" id = "at_44_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "0.0 7.51 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "44" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_45_4aa8e020" uuid = "4aa8e020" id = "at_45_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-0.0 9.01 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "45" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_46_4aa8e020" uuid = "4aa8e020" id = "at_46_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-1.3 8.26 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "46" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_47_4aa8e020" uuid = "4aa8e020" id = "at_47_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "0.0 7.51 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "47" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_48_4aa8e020" uuid = "4aa8e020" id = "at_48_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "3.9 2.25 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "48" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_49_4aa8e020" uuid = "4aa8e020" id = "at_49_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "5.2 1.5 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "49" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_50_4aa8e020" uuid = "4aa8e020" id = "at_50_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "6.5 0.75 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "50" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_51_4aa8e020" uuid = "4aa8e020" id = "at_51_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "6.5 2.25 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "51" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_52_4aa8e020" uuid = "4aa8e020" id = "at_52_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "5.2 1.5 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "52" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_53_4aa8e020" uuid = "4aa8e020" id = "at_53_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "6.5 0.75 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "53" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_54_4aa8e020" uuid = "4aa8e020" id = "at_54_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "2.6 4.5 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "54" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_55_4aa8e020" uuid = "4aa8e020" id = "at_55_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "3.9 3.75 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "55" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_56_4aa8e020" uuid = "4aa8e020" id = "at_56_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "5.2 3.0 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "56" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_57_4aa8e020" uuid = "4aa8e020" id = "at_57_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "5.23 4.49 24.28" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "57" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_58_4aa8e020" uuid = "4aa8e020" id = "at_58_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "3.9 3.75 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "58" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_59_4aa8e020" uuid = "4aa8e020" id = "at_59_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "5.2 3.0 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "59" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_60_4aa8e020" uuid = "4aa8e020" id = "at_60_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "1.3 6.76 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "60" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_61_4aa8e020" uuid = "4aa8e020" id = "at_61_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "2.6 6.0 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "61" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_62_4aa8e020" uuid = "4aa8e020" id = "at_62_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "3.9 5.25 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "62" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_63_4aa8e020" uuid = "4aa8e020" id = "at_63_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "3.91 6.78 24.34" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "63" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_64_4aa8e020" uuid = "4aa8e020" id = "at_64_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "2.6 6.0 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "64" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_65_4aa8e020" uuid = "4aa8e020" id = "at_65_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "3.9 5.25 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "65" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_66_4aa8e020" uuid = "4aa8e020" id = "at_66_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "5.2 0.0 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "66" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_67_4aa8e020" uuid = "4aa8e020" id = "at_67_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "1.3 8.26 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "67" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_68_4aa8e020" uuid = "4aa8e020" id = "at_68_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "2.6 7.51 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "68" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_69_4aa8e020" uuid = "4aa8e020" id = "at_69_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "7.8 0.02 24.3" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "69" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_70_4aa8e020" uuid = "4aa8e020" id = "at_70_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "1.3 8.26 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "70" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_71_4aa8e020" uuid = "4aa8e020" id = "at_71_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "2.6 7.51 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "71" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_72_4aa8e020" uuid = "4aa8e020" id = "at_72_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "6.5 2.25 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "72" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_73_4aa8e020" uuid = "4aa8e020" id = "at_73_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "7.8 1.5 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "73" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_74_4aa8e020" uuid = "4aa8e020" id = "at_74_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "9.1 0.75 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "74" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_75_4aa8e020" uuid = "4aa8e020" id = "at_75_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "9.1 2.25 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "75" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_76_4aa8e020" uuid = "4aa8e020" id = "at_76_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "7.8 1.5 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "76" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_77_4aa8e020" uuid = "4aa8e020" id = "at_77_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "9.1 0.75 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "77" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_78_4aa8e020" uuid = "4aa8e020" id = "at_78_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "5.2 4.5 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "78" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_79_4aa8e020" uuid = "4aa8e020" id = "at_79_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "6.5 3.75 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "79" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_80_4aa8e020" uuid = "4aa8e020" id = "at_80_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "7.8 3.0 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "80" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_81_4aa8e020" uuid = "4aa8e020" id = "at_81_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "7.8 4.5 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "81" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_82_4aa8e020" uuid = "4aa8e020" id = "at_82_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "6.5 3.75 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "82" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_83_4aa8e020" uuid = "4aa8e020" id = "at_83_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "7.8 3.0 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "83" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_84_4aa8e020" uuid = "4aa8e020" id = "at_84_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "3.9 6.76 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "84" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_85_4aa8e020" uuid = "4aa8e020" id = "at_85_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "5.2 6.0 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "85" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_86_4aa8e020" uuid = "4aa8e020" id = "at_86_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "6.5 5.25 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "86" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_87_4aa8e020" uuid = "4aa8e020" id = "at_87_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "-3.88 6.76 24.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "87" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_88_4aa8e020" uuid = "4aa8e020" id = "at_88_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "5.2 6.0 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "88" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_89_4aa8e020" uuid = "4aa8e020" id = "at_89_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "6.5 5.25 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "89" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_90_4aa8e020" uuid = "4aa8e020" id = "at_90_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "7.8 0.0 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "90" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_91_4aa8e020" uuid = "4aa8e020" id = "at_91_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "3.9 8.26 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "91" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_92_4aa8e020" uuid = "4aa8e020" id = "at_92_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "5.2 7.51 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "92" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_93_4aa8e020" uuid = "4aa8e020" id = "at_93_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "0.0 0.01 24.3" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "93" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_94_4aa8e020" uuid = "4aa8e020" id = "at_94_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "3.9 8.26 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "94" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_95_4aa8e020" uuid = "4aa8e020" id = "at_95_4aa8e020" radius = "1.32" name = "at_4aa8e020" translation = "5.2 7.51 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "95" uuid = "4aa8e020" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Group> 
       <Shape > 
         <IndexedLineSet coordIndex = "0 1 -1 0 2 -1 0 4 -1 1 3 -1 1 5 -1 2 3 -1 2 6 -1 3 7 -1 4 5 -1 4 6 -1 5 7 -1 6 7 -1" > 
           <Coordinate point = "[[ 0.          0.          0.          0.          0.         38.213406
  -5.2001865   9.00698723  0.         -5.2001865   9.00698723 38.213406
  10.400373    0.          0.         10.400373    0.         38.213406
   5.2001865   9.00698723  0.          5.2001865   9.00698723 38.213406  ]]" > 
           </Coordinate> 
         </IndexedLineSet> 
         <Appearance > 
           <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
           </Material> 
         </Appearance> 
       </Shape> 
       <Switch id = "switch_marker_0_4aa8e020" whichChoice = "-1" > 
         <Transform id = "marker_0_4aa8e020" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_1_4aa8e020" whichChoice = "-1" > 
         <Transform id = "marker_1_4aa8e020" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_2_4aa8e020" whichChoice = "-1" > 
         <Transform id = "marker_2_4aa8e020" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_3_4aa8e020" whichChoice = "-1" > 
         <Transform id = "marker_3_4aa8e020" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_4_4aa8e020" whichChoice = "-1" > 
         <Transform id = "marker_4_4aa8e020" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_line_0_4aa8e020" whichChoice = "-1" > 
         <Shape > 
           <IndexedLineSet id = "line_ind_0_4aa8e020" solid = "false" coordIndex = "0 1 -1" > 
             <Coordinate id = "line_coor_0_4aa8e020" point = "0 0 0 0 0 1" > 
             </Coordinate> 
           </IndexedLineSet> 
           <Appearance > 
             <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
             </Material> 
           </Appearance> 
         </Shape> 
       </Switch> 
       <Switch id = "switch_line_1_4aa8e020" whichChoice = "-1" > 
         <Shape > 
           <IndexedLineSet id = "line_ind_1_4aa8e020" solid = "false" coordIndex = "0 1 -1" > 
             <Coordinate id = "line_coor_1_4aa8e020" point = "0 0 0 0 0 1" > 
             </Coordinate> 
           </IndexedLineSet> 
           <Appearance > 
             <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
             </Material> 
           </Appearance> 
         </Shape> 
       </Switch> 
       <TimeSensor DEF = "time" cycleInterval = "10" loop = "true" > 
       </TimeSensor> 
       <PositionInterpolator DEF = "move_0" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_0" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_0" fromField = "value_changed" toNode = "at_0_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_1" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_1" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_1" fromField = "value_changed" toNode = "at_1_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_2" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_2" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_2" fromField = "value_changed" toNode = "at_2_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_3" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.2852710112821855 2.228563885615447 24.289538084187463 1.2686872076239182 2.2104901472150313 24.27908212993554 1.2532952554803691 2.200517102262612 24.26018091081427 1.2383795179611985 2.205211459551152 24.22397293597399 1.221495825913049 2.2295790502499355 24.159023231260033 1.205154633093369 2.280636050381942 24.049064029233584 1.2229050751870538 2.3850137279067702 23.90007979551411 1.2649525338829237 2.484446451363661 23.880581643920358 1.297406816663865 2.426586050838302 24.07731284178854 1.301180592647093 2.2508837351609903 24.30350062690284 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_3" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_3" fromField = "value_changed" toNode = "at_3_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_4" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_4" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_4" fromField = "value_changed" toNode = "at_4_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_5" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_5" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_5" fromField = "value_changed" toNode = "at_5_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_6" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_6" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_6" fromField = "value_changed" toNode = "at_6_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_7" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_7" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_7" fromField = "value_changed" toNode = "at_7_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_8" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_8" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_8" fromField = "value_changed" toNode = "at_8_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_9" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.025630852329713474 4.49912024674394 24.290422351474614 -0.029920169494316865 4.444541707984436 24.233777236332923 -0.04307952800812902 4.402520672627535 24.15417612602154 -0.06631746401984172 4.378658806805659 24.026552449683834 -0.09589536379768054 4.418165863234565 23.86523631547942 -0.12629726231728627 4.55924636690994 23.830057924329466 -0.11721810314291033 4.6267442072183105 23.9938360159063 -0.07364878870150714 4.606772645543144 24.145680928104706 -0.03604328990285727 4.563583577610334 24.239725557092108 4.396726601896878e-05 4.506468467617166 24.306850371938935 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_9" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_9" fromField = "value_changed" toNode = "at_9_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_10" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_10" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_10" fromField = "value_changed" toNode = "at_10_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_11" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_11" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_11" fromField = "value_changed" toNode = "at_11_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_12" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_12" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_12" fromField = "value_changed" toNode = "at_12_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_13" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_13" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_13" fromField = "value_changed" toNode = "at_13_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_14" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_14" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_14" fromField = "value_changed" toNode = "at_14_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_15" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.3037257019020758 6.756331450177707 24.306625111701425 -1.2592665908225034 6.630015461231054 24.13778032866576 -1.254036742133228 6.617025287567452 23.98778285274508 -1.3021715084936905 6.722968170389342 24.005702555079303 -1.3177713446425805 6.785516828685478 24.132805403287538 -1.3234666597329106 6.824267271906215 24.214976226326797 -1.324642729419901 6.8419515337023205 24.265561822683843 -1.319693094873793 6.831465970527524 24.292800172641368 -1.310357342596504 6.800026149044648 24.304359649989046 -1.2988237890533971 6.758108395034624 24.308220363006154 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_15" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_15" fromField = "value_changed" toNode = "at_15_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_16" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_16" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_16" fromField = "value_changed" toNode = "at_16_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_17" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_17" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_17" fromField = "value_changed" toNode = "at_17_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_18" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_18" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_18" fromField = "value_changed" toNode = "at_18_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_19" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_19" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_19" fromField = "value_changed" toNode = "at_19_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_20" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_20" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_20" fromField = "value_changed" toNode = "at_20_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_21" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.6003048644486473 9.003735166366159 24.305822535654695 -2.167691287269802 8.311089899821201 24.948694066768837 -1.6742967898292955 7.456021271284278 25.55832735392217 -1.0741834006926858 6.4120214091241285 25.94361380718732 -0.43640522728695563 5.324249286151873 26.106337184429748 0.27931733868324476 4.160064440176921 26.043971347207002 1.091290539385466 3.1101012651000888 25.7939870667082 2.0406128339820793 1.9649354699869022 25.491566285641557 2.356691339985932 0.8836600968656945 24.91746848483159 2.597052949973667 0.004637914595010559 24.300787133478703 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_21" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_21" fromField = "value_changed" toNode = "at_21_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_22" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_22" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_22" fromField = "value_changed" toNode = "at_22_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_23" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_23" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_23" fromField = "value_changed" toNode = "at_23_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_24" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_24" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_24" fromField = "value_changed" toNode = "at_24_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_25" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_25" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_25" fromField = "value_changed" toNode = "at_25_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_26" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_26" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_26" fromField = "value_changed" toNode = "at_26_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_27" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.912710644692814 2.229280869141756 24.290492977199953 3.872616829676891 2.2302630891302067 24.240678801308643 3.8453416616949987 2.2545387332438076 24.172292909303387 3.8312206769262174 2.3246716630505255 24.06475726478484 3.8624305565001933 2.437147598233412 23.92908358798751 3.9498305554149256 2.484675310877722 23.87352993347812 3.9587848646004407 2.475123263384537 23.834076380292576 3.9013703692069237 2.5248994028787273 23.906284768972288 3.8921940284842504 2.444079825378298 24.1064913060842 3.900942778374182 2.2539099098207926 24.307030382651106 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_27" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_27" fromField = "value_changed" toNode = "at_27_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_28" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_28" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_28" fromField = "value_changed" toNode = "at_28_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_29" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_29" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_29" fromField = "value_changed" toNode = "at_29_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_30" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_30" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_30" fromField = "value_changed" toNode = "at_30_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_31" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_31" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_31" fromField = "value_changed" toNode = "at_31_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_32" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_32" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_32" fromField = "value_changed" toNode = "at_32_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_33" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.5879023741143854 4.486597847722434 24.370256456901597 2.6152382962889593 4.459413479146484 24.29613922634193 2.6441197583333267 4.444402047760933 24.19214667807417 2.682364377701198 4.441497233671498 24.020551866548267 2.7134208221693403 4.479990764734214 23.76459122615632 2.5928343693397675 4.693053846023286 23.73587587282373 2.565377456029106 4.71148305688064 23.966357373706536 2.5643791270057483 4.663670828890694 24.13293130834437 2.5800112266750053 4.592286628886248 24.22887772256689 2.605055359344784 4.5076093063515295 24.288446971856395 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_33" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_33" fromField = "value_changed" toNode = "at_33_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_34" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_34" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_34" fromField = "value_changed" toNode = "at_34_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_35" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_35" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_35" fromField = "value_changed" toNode = "at_35_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_36" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_36" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_36" fromField = "value_changed" toNode = "at_36_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_37" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_37" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_37" fromField = "value_changed" toNode = "at_37_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_38" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_38" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_38" fromField = "value_changed" toNode = "at_38_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_39" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.27240363623041 6.770457952058309 24.27887620453943 1.382296368212132 6.687068451658682 24.09664364421658 1.4396017614509748 6.6987135093817525 23.909095013125913 1.3850533368057525 6.843964506888444 23.8684274239926 1.3157603253598673 6.906347493605002 24.030793839763746 1.2736313678443871 6.909680833283813 24.157230739002493 1.2524584294092729 6.892816639946679 24.232943425380665 1.2511684571715929 6.855609665521557 24.272563229100154 1.263450687127093 6.803552793686346 24.290471280355376 1.2825792438615928 6.744289769801695 24.298438396343236 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_39" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_39" fromField = "value_changed" toNode = "at_39_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_40" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_40" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_40" fromField = "value_changed" toNode = "at_40_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_41" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_41" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_41" fromField = "value_changed" toNode = "at_41_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_42" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_42" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_42" fromField = "value_changed" toNode = "at_42_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_43" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_43" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_43" fromField = "value_changed" toNode = "at_43_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_44" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_44" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_44" fromField = "value_changed" toNode = "at_44_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_45" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.0002248425801898188 9.006066033303107 24.306802193629586 0.32361542878756727 8.316463248492898 24.843306677421992 0.7104326872717912 7.479310451660446 25.336955203336885 1.2192579638339756 6.432343229862761 25.58483664475502 1.8757793161171306 5.268451695743486 25.58249704094593 2.887240165056798 3.819028113624243 25.663491939359368 3.67161162005331 2.6658689647689853 25.666462191385655 4.31819550282679 1.5677182774263092 25.37713473059024 4.799550282089098 0.7044260814394859 24.853192606017554 5.204225038028712 0.0020967613865172517 24.300809667400795 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_45" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_45" fromField = "value_changed" toNode = "at_45_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_46" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_46" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_46" fromField = "value_changed" toNode = "at_46_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_47" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_47" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_47" fromField = "value_changed" toNode = "at_47_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_48" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_48" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_48" fromField = "value_changed" toNode = "at_48_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_49" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_49" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_49" fromField = "value_changed" toNode = "at_49_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_50" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_50" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_50" fromField = "value_changed" toNode = "at_50_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_51" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.5044053586658475 2.2481143406561883 24.30760221933834 6.370453748277321 2.33131533094344 24.15635477474094 6.354722204126246 2.411642681504046 24.02210078478363 6.4621582955636105 2.402233119790524 24.05350086478403 6.517626752930137 2.3397072694381515 24.157307921857797 6.552706262622281 2.300107457199617 24.224488293429925 6.566980075046825 2.2793353981742572 24.26428192002187 6.563521008444233 2.2701049641328135 24.28575433544208 6.545708987064484 2.267135980123078 24.29432947138372 6.501372104304568 2.24980663763322 24.301059724506434 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_51" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_51" fromField = "value_changed" toNode = "at_51_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_52" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_52" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_52" fromField = "value_changed" toNode = "at_52_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_53" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_53" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_53" fromField = "value_changed" toNode = "at_53_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_54" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_54" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_54" fromField = "value_changed" toNode = "at_54_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_55" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_55" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_55" fromField = "value_changed" toNode = "at_55_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_56" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_56" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_56" fromField = "value_changed" toNode = "at_56_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_57" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.229104512232149 4.485467560920783 24.277609411241063 5.226901609723619 4.520101560394447 24.28404745428355 5.230915419614822 4.547663547994914 24.288862081257715 5.244890322717459 4.5624145431867245 24.2890842854106 5.266722362482158 4.573201141806765 24.284599282164315 5.294138155440632 4.589092081415937 24.27554687312802 5.289213352860771 4.59560934054983 24.278951198466938 5.26343298670178 4.575921677192945 24.293415963053327 5.232240391235559 4.544473634971062 24.300213227568708 5.196047674219077 4.505617864801554 24.30118230044136 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_57" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_57" fromField = "value_changed" toNode = "at_57_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_58" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_58" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_58" fromField = "value_changed" toNode = "at_58_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_59" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_59" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_59" fromField = "value_changed" toNode = "at_59_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_60" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_60" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_60" fromField = "value_changed" toNode = "at_60_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_61" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_61" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_61" fromField = "value_changed" toNode = "at_61_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_62" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_62" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_62" fromField = "value_changed" toNode = "at_62_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_63" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.910902287092594 6.775543367105439 24.342124225395853 3.9409321350786173 6.777552047780751 24.334060138517525 3.9649285129348093 6.783383272126267 24.32238329376176 3.979942938788984 6.793717076478702 24.306382412524382 3.980656281254218 6.801068028984036 24.29605418929068 3.9586685652464255 6.787513190473064 24.301158125038775 3.9385061573322475 6.769690666590545 24.307875459279973 3.9206967156386625 6.753366476794623 24.315759255266528 3.909279040143603 6.749255530170034 24.328210985290927 3.9005054923431444 6.755503393567644 24.34700213991426 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_63" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_63" fromField = "value_changed" toNode = "at_63_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_64" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_64" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_64" fromField = "value_changed" toNode = "at_64_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_65" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_65" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_65" fromField = "value_changed" toNode = "at_65_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_66" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_66" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_66" fromField = "value_changed" toNode = "at_66_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_67" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_67" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_67" fromField = "value_changed" toNode = "at_67_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_68" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_68" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_68" fromField = "value_changed" toNode = "at_68_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_69" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "7.796133286659032 0.017411357712105887 24.29530482713749 2.6187111264328156 8.953116285302634 24.27043039847406 2.640640947272593 8.945735587159547 24.264103305541806 2.639810314334358 8.997141586382218 24.291817134004255 7.837030973212151 0.023162827949950393 24.30887560113085 7.830499382811911 0.036447685592353125 24.318794084460944 7.82259776460672 0.034825138763887584 24.325231860989618 7.8150927833147925 0.025899118233237685 24.330627708719586 7.8069754384975445 0.014221444089116162 24.336540302897724 7.799899091190856 0.0007915587399410709 24.344030930140484 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_69" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_69" fromField = "value_changed" toNode = "at_69_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_70" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_70" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_70" fromField = "value_changed" toNode = "at_70_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_71" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_71" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_71" fromField = "value_changed" toNode = "at_71_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_72" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_72" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_72" fromField = "value_changed" toNode = "at_72_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_73" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_73" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_73" fromField = "value_changed" toNode = "at_73_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_74" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_74" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_74" fromField = "value_changed" toNode = "at_74_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_75" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "9.097014909158794 2.250018707423952 24.307413527985673 8.247145172165032 1.9945901033038946 24.89832655092787 7.193304025315592 1.6503434006763733 25.427397044122216 5.934281042499394 1.3127250704156181 25.67917467404962 4.586822464481219 1.0536224687368811 25.76371931998171 3.1005397437156295 0.9248424470562486 25.651128073713842 1.6388642152855797 0.971407387397838 25.406681989800667 0.1384133797782279 1.4525006827130742 25.11850461591369 -0.6764159240029054 1.9152089363787428 24.72334752211385 -1.298826719394734 2.2749287609590314 24.29844025211366 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_75" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_75" fromField = "value_changed" toNode = "at_75_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_76" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_76" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_76" fromField = "value_changed" toNode = "at_76_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_77" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_77" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_77" fromField = "value_changed" toNode = "at_77_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_78" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_78" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_78" fromField = "value_changed" toNode = "at_78_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_79" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_79" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_79" fromField = "value_changed" toNode = "at_79_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_80" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_80" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_80" fromField = "value_changed" toNode = "at_80_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_81" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "7.800704187785526 4.502124815090319 24.307837212709597 7.786386345203826 4.491734085287828 24.304362931491326 7.777546423417669 4.488120016135899 24.297365212097446 7.771313625671092 4.490744135387248 24.28601591848244 7.771536593412296 4.500973485130553 24.28376643309233 7.78447613600376 4.5106156665328 24.294392443522323 7.793655397139676 4.513490036132659 24.3024296267782 -2.601901996355343 4.512748745976876 24.30602155750838 -2.600380451118058 4.509519658111312 24.30714288669787 7.799157678439427 4.504027136738051 24.307218527193186 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_81" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_81" fromField = "value_changed" toNode = "at_81_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_82" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_82" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_82" fromField = "value_changed" toNode = "at_82_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_83" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_83" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_83" fromField = "value_changed" toNode = "at_83_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_84" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_84" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_84" fromField = "value_changed" toNode = "at_84_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_85" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_85" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_85" fromField = "value_changed" toNode = "at_85_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_86" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_86" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_86" fromField = "value_changed" toNode = "at_86_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_87" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.8825680011940267 6.757014070311783 24.29459035395583 -3.8828131322109014 6.751983035221498 24.29098701037552 -3.867807373161507 6.762057001248353 24.294133823479743 -3.8579587424668476 6.770020961716781 24.297099044974097 -3.854166587889549 6.774162116068712 24.299017064140646 -3.8530881240491577 6.771979754012867 24.299413494363733 -3.8530707755986926 6.761570551530877 24.2987658479326 -3.8553748113588497 6.751978055096208 24.29948370776098 -3.8657680361670357 6.747274192050819 24.29886874974624 -3.8810194214810325 6.746315807575217 24.297723338610304 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_87" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_87" fromField = "value_changed" toNode = "at_87_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_88" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_88" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_88" fromField = "value_changed" toNode = "at_88_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_89" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_89" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_89" fromField = "value_changed" toNode = "at_89_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_90" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_90" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_90" fromField = "value_changed" toNode = "at_90_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_91" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_91" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_91" fromField = "value_changed" toNode = "at_91_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_92" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_92" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_92" fromField = "value_changed" toNode = "at_92_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_93" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.0043635330345542025 0.008559828607489805 24.297833334834586 0.00919326838966078 0.005047796040072283 24.297320413931573 0.01357529951104756 0.0022632754701081854 24.29502287870112 -5.184118104834845 9.005253346625164 24.289892755579018 -5.185078003044883 8.993673152835349 24.280831623479216 5.206660051235095 8.961830835015158 24.264686019320497 5.188271817169292 8.895171854719054 24.235456293721267 5.197860873379952 8.812216097629936 24.21423031056817 5.215929915549696 8.886786176892077 24.27825621666181 -1.4000982320326613e-05 0.0024914315676172745 24.346629549542026 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_93" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_93" fromField = "value_changed" toNode = "at_93_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_94" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_94" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_94" fromField = "value_changed" toNode = "at_94_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_95" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_95" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_95" fromField = "value_changed" toNode = "at_95_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_96" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.624578109042964 4.544935320212249 26.438483253732468 2.4849512062025614 4.378612204516566 26.677373215783923 2.353857990430162 4.21282524310942 26.95299086869079 2.23082936774741 4.039705261378582 27.29560687337326 2.106032406900843 3.8936764363323544 27.702173920080053 1.965180630070094 3.880704675714488 27.990885911890977 1.8360027480583074 3.873709479039123 28.000313364189946 1.6696645291424503 3.7806632572568653 27.868621172086886 1.5232975080319044 3.6744396748441432 27.787937649161467 1.3860358214770436 3.575080445641074 27.757995549739015 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_96" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_96" fromField = "value_changed" toNode = "at_96_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_97" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.7520521804625546 6.453228960744443 26.476685452320268 3.6353020799326634 6.276607502858568 26.65905864750451 3.5543314751003354 6.153033082032822 26.888707650946955 3.5032089611705746 6.080737170395247 27.147385248522337 3.415199805724221 6.064069809324183 27.366514146503647 3.2225235266953214 6.011447411904049 27.43305745525369 3.044972935155932 5.917884407251576 27.464842338767653 2.8841170707590864 5.8208629753532914 27.523353508167666 2.729452413663911 5.715863899558674 27.608090997880012 2.5767486186834434 5.604993826724348 27.706600094000084 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_97" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_97" fromField = "value_changed" toNode = "at_97_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_98" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.1255048000722034 5.3913014668806865 27.18237094006607 3.000643047486271 5.2652590536476955 27.346392073057714 2.892631191494735 5.155528522563948 27.51908625547483 2.8079778032414087 5.069933974275087 27.72217380565947 2.711142250777472 5.023303422669521 27.93360149301194 2.5489259481257727 5.014658681498103 27.965174153048824 2.3982977013977864 4.923142143806445 27.8676600095272 2.25196857145665 4.807112049690502 27.785069528760136 2.1142778566829783 4.695285697724916 27.74517913656541 1.981417330527889 4.589936520963488 27.729831888455397 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_98" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_98" fromField = "value_changed" toNode = "at_98_4aa8e020" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_99" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.125591223648816 7.081860309273324 27.127527904912807 4.173756287111867 7.361691928155314 26.953104773394895 4.184482799981539 7.5699923342166775 26.758180329361842 4.164635248795064 7.72093361217838 26.56300250391933 4.120000338957454 7.832901432560707 26.370829516411312 4.063731721503044 7.920044564675382 26.17920927721698 4.0062571341596716 7.997171997346243 25.978638869976855 3.955263225323466 8.071867642201054 25.758633926955678 3.9234437431156595 8.162898351706815 25.50920694514357 3.9012618588625823 8.261142839433239 25.23474031697266 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_99" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_99" fromField = "value_changed" toNode = "at_99_4aa8e020" toField = "translation" > 
       </ROUTE> 
     </Scene> 
   </X3D> 
 </div> 

<script type="text/javascript" src="https://code.jquery.com/jquery-2.1.0.min.js" ></script>
 <script >
 if (atoms_dict == undefined) {var atoms_dict = {"new": true};}; 
atoms_dict["4aa8e020"] = {com: "2.50 4.32 19.14", 
top_pos: "2.50 4.32 58.15", 
front_pos: "2.50 -34.68 19.14", 
right_pos: "41.50 4.32 19.14", 
left_pos: "-36.50 4.32 19.14", 
top_ori: "0 0 0 0", 
front_ori: "1 0 0 1.57079", 
right_ori: "0 1 0 1.57079", 
left_ori: "0 1 0 -1.57079", 
select: [], 
uuid: "4aa8e020", 
label: "False", 
bond: "False", 
polyhedra: {}, 
};

// if (atoms_dict[uuid]['new']){
function setQuality(uuid, quality){
        // $('#error_'.concat(atoms_dict[uuid]['uuid'])).html('uuid: '.concat(atoms_dict[uuid]["uuid"]));
        var x3d = 'x3dase_' + uuid;
        document.getElementById(x3d).setAttribute('PrimitiveQuality', quality);
    }
function set_viewpoint(uuid, pos, ori){
    // $('#error_'.concat(atoms_dict[uuid]['uuid'])).html('uuid: '.concat(atoms_dict[uuid]["uuid"]));
    var persp = 'camera_persp_' + uuid;
    var ortho = 'camera_ortho_' + uuid;
    document.getElementById(persp).setAttribute('orientation', atoms_dict[uuid][ori]);
    document.getElementById(persp).setAttribute('position', atoms_dict[uuid][pos]);
    document.getElementById(ortho).setAttribute('orientation', atoms_dict[uuid][ori]);
    document.getElementById(ortho).setAttribute('position', atoms_dict[uuid][pos]);
}

//Round a float value to x.xx format
function roundWithTwoDecimals(value)
{
    var x = (Math.round(value * 100)) / 100;
    var y = x.toFixed(2);
    return y;
}
//Handle click on any group member
function handleGroupClick(event)
{
    //Mark hitting point
    var target = event.target;
    var uuid = target.parentNode.getAttribute('uuid')
    var radius = target.parentNode.getAttribute('radius');
    var scale = target.parentNode.getAttribute('scale');
    scale = parseFloat(radius)*parseFloat(scale)*1.2;
    var scale = ' ' + scale + ' ' + scale + ' ' + scale;
    var translation = target.parentNode.getAttribute('translation');
    var id = target.parentNode.getAttribute('id');
    if (window.event.ctrlKey) {
        atoms_dict[uuid]['select'].push(id);

    }
    else {
        for (var i=1; i<= atoms_dict[uuid]['select'].length; i++) {
            $('#switch_marker_' + i + '_' + uuid).attr('whichChoice', -1);
        }
        atoms_dict[uuid]['select'] = [];
        atoms_dict[uuid]['select'].push(id);
        $('#switch_marker_' + 2 + '_' + uuid).attr('whichChoice', -1);
}
    var n = atoms_dict[uuid]['select'].length;
    $('#switch_marker_' + n + '_' + uuid).attr('whichChoice', 0);
    $('#marker_' + n + '_' + uuid).attr('translation', translation);
    $('#marker_' + n + '_' + uuid).attr('scale', scale);
    var atom_kind = '#lastonMouseoverObject_kind_'.concat(uuid);
    var atom_index = '#lastonMouseoverObject_index_'.concat(uuid);
    $(atom_kind).html(target.getAttribute("kind"));
    $(atom_index).html(target.getAttribute("index"));
    //
    var coord = translation.split(" ")
    var atom_position = '#position_'.concat(uuid);
    var x = roundWithTwoDecimals(coord[0]);
    var y = roundWithTwoDecimals(coord[1]);
    var z = roundWithTwoDecimals(coord[2]);
    var position = 'x = ' + x + ' y = ' + y + ' z = ' + z;
    $(atom_position).html(position);

    if (atoms_dict[uuid]['select'].length == 2){
        calculate_distance(uuid);
        draw_line(uuid);
    }
    else if (atoms_dict[uuid]['select'].length == 3){
        calculate_angle(uuid);
        draw_line(uuid);
    }

    console.log(event);
}
//Add a onMouseover callback to every shape
$(document).ready(function(){
    $("shape").each(function() {
        // $(this).attr("onMouseover", "handleOnMouseover_shape(this)");
        // $(this).attr("onclick", "handleClick_shape(this)");
    });
    //Add a onMouseover callback to every transform
    $("transform").each(function() {
        // $(this).attr("onMouseover", "handleOnMouseover_transform(this)");
        // $(this).attr("onclick", "handleClick_transform(this)");
    });
});
$(document).on("click", function(e) {
    if (e.target === document || e.target.tagName === "BODY" || e.target.tagName === "HTML") {
        $('#marker').attr('scale', "0.0001 0.0001 0.0001");
    }
});
//Handle onMouseover on a shape
function handleOnMouseover_shape(shape)
{
    var atom_kind = '#lastonMouseoverObject_kind_'.concat($(shape).attr("uuid"));
    var atom_index = '#lastonMouseoverObject_index_'.concat($(shape).attr("uuid"));
    $(atom_kind).html($(shape).attr("kind"));
    $(atom_index).html($(shape).attr("index"));
}
//Handle onMouseover on a shape
function handleClick_shape(shape)
{
    var atom_kind = '#lastonMouseoverObject_kind_'.concat($(shape).attr("uuid"));
    var atom_index = '#lastonMouseoverObject_index_'.concat($(shape).attr("uuid"));
    $(atom_kind).html($(shape).attr("kind"));
    $(atom_index).html($(shape).attr("index"));
}
//Handle onMouseover on a transform
function handleOnMouseover_transform(transform)
{
    var atom_position = '#position_'.concat($(transform).attr("uuid"));
    var coord = $(transform).attr("translation").split(" "[0]);
    var x = roundWithTwoDecimals(coord[0]);
    var y = roundWithTwoDecimals(coord[1]);
    var z = roundWithTwoDecimals(coord[2]);
    var position = 'x = ' + x + ' y = ' + y + ' z = ' + z;
    $(atom_position).html(position);
}

function calculate_distance(uuid)
{
    var measure = '#measure_'.concat(uuid);
    var c1 = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation").split(" ");
    var c2 = document.getElementById(atoms_dict[uuid]['select'][1]).getAttribute("translation").split(" ");
    r = (c1[0] - c2[0])*(c1[0] - c2[0]) + (c1[1] - c2[1])*(c1[1] - c2[1]) + (c1[2] - c2[2])*(c1[2] - c2[2]);
    r = roundWithTwoDecimals(Math.sqrt(r));
    var dist = 'Distance:  ' + r;
    $(measure).html(dist);
}
function calculate_angle(uuid)
{
    var measure = '#measure_'.concat(uuid);
    var c1 = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation").split(" ");
    var c2 = document.getElementById(atoms_dict[uuid]['select'][1]).getAttribute("translation").split(" ");
    var c3 = document.getElementById(atoms_dict[uuid]['select'][2]).getAttribute("translation").split(" ");
    var AB = Math.sqrt(Math.pow(c2[0]-c1[0],2)+ Math.pow(c2[1]-c1[1],2) + Math.pow(c2[2]-c1[2],2));    
    var BC = Math.sqrt(Math.pow(c2[0]-c3[0],2)+ Math.pow(c2[1]-c3[1],2) + Math.pow(c2[2]-c3[2],2)); 
    var AC = Math.sqrt(Math.pow(c3[0]-c1[0],2)+ Math.pow(c3[1]-c1[1],2)+ Math.pow(c3[2]-c1[2],2));
    var angle = roundWithTwoDecimals(Math.acos((BC*BC+AB*AB-AC*AC)/(2*BC*AB))*180/3.1415926);
    var angle = 'angle:  ' + angle;
    $(measure).html(angle);
}
function draw_line(uuid)
{
    var n = atoms_dict[uuid]['select'].length;
    var coordIndex = '';
    var point = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation");
    for (var i = 1; i < n; i++) {
        var c1 = document.getElementById(atoms_dict[uuid]['select'][i]).getAttribute("translation");
        coordIndex = coordIndex + (i-1) + ' ' + i + ' -1 ';
        point = point + ' ' + c1 + ' ';
    }
    $('#line_coor_' + 0 + '_' + uuid).attr('point', point);
    $('#line_ind_' + 0 + '_' + uuid).attr('coordIndex', coordIndex);
    $('#switch_line_' + 0 + '_' + uuid).attr('whichChoice', 0);
}
//Handle models
function spacefilling(uuid)
{
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "1.0, 1.0, 1.0");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function ballstick(uuid)
{
    if (atoms_dict[uuid]['bond']=='False'){ 
        alert('Please set bond parameter in your code, e.g. bond=1.0!');
        $('#error_'.concat(uuid)).html('(^_^) Please set bond parameter in your code, e.g. bond=1.0!');
		return ;
    }
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "0.6, 0.6, 0.6");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function polyhedra(uuid)
{
    if (atoms_dict[uuid]['polyhedra'].length==0){ 
        alert('Please set polyhedra parameter in your code, e.g. polyhedra={"Ti": ["O"]}!');
        $('#error_'.concat(uuid)).html('(^_^) Please set polyhedra parameter in your code, e.g. polyhedra={"Ti": ["O"]}!');
		return ;
    }
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "0.6, 0.6, 0.6");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '0');
}
function none(uuid)
{
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.0");
        }
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
        
function element(uuid)
{
    if (atoms_dict[uuid]['label']=='False'){ 
        alert('To show element, please set label=True in your code!');
        $('#error_'.concat(uuid)).html('(^_^) To show element, please set label=True in your code!');
		return ;
	}
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.4");
        }
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function index(uuid)
{
    if (atoms_dict[uuid]['label']=='False'){ 
        alert('To show index, please set label=True in your code!');
        $('#error_'.concat(uuid)).html('(^_^) To show index, please set label=True in your code!');
		return ;
	}
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.4");
        }
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
// } 
</script> </body>
</html>
