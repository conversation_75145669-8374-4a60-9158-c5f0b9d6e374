<html>
<head>
<title>ASE atomic visualization</title>
<link rel="stylesheet" type="text/css"
 href="https://www.x3dom.org/x3dom/release/x3dom.css">
</link>
<script type="text/javascript"
 src="https://www.x3dom.org/x3dom/release/x3dom.js">
</script>
<style>
* {
    box-sizing: border-box;
  }
 
/* Create two unequal columns that floats next to each other */
.column {
  float: left;
  padding: 1px;
}

.left {
  width: 20%;
}

.right {
  width: 80%;
}

/* Clear floats after the columns */
.row:after {
  content: "";
  display: table;
  clear: both;
}

#x3dase{
    top:0;
    width: 80%;
    height: 80%;
    border:2px solid darkorange;        
}

#sidebar{
    top:0;
    border:2px solid darkorange;        
}


/* Sidebar component containers */
.ui-widget-header
{
  background-color: lightblue;
  font-size: 12px;

}
.sidebarComponent
{
    padding:2px 2px 2px 2px;
    font-size: medium;
}

.button {
  background-color: #4CAF50; /* Green */
  border: 1px solid green;
  color: white;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 10px;
  cursor: pointer;
  /* float: left; */
}

</style></head>
<body>
<div class = "column left", id = "sidebar">

    <div class="ui-widget-header">Model</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="spacefilling('b81e5543')">Ball</button>
            <button type="button" class = "button" onclick="ballstick('b81e5543')">Ball-and-stick</button>
            <button type="button" class = "button" onclick="polyhedra('b81e5543')">Polyhedra</button>
        </form>
    </div>
    <div class="ui-widget-header">Label</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="none('b81e5543')">None</button>
            <button type="button" class = "button" onclick="element('b81e5543')"> Element</button>
            <button type="button" class = "button" onclick="index('b81e5543')">Index</button>
        </form>
    </div>

    <div class="ui-widget-header">Camera</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="document.getElementById('camera_ortho_b81e5543').setAttribute('set_bind','true');">Orthographic</button>
            <button type="button" class = "button" onclick="document.getElementById('camera_persp_b81e5543').setAttribute('set_bind','true');">Perspective</button>
        </form>
    </div>

    <div class="ui-widget-header">View</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="set_viewpoint('b81e5543', 'top_pos', 'top_ori')">Top</button>
            <button type="button" class = "button" onclick="set_viewpoint('b81e5543', 'front_pos', 'front_ori')">Front</button>
            <button type="button" class = "button" onclick="set_viewpoint('b81e5543', 'right_pos', 'right_ori')">Right</button>
        </form>
    </div>

    <div class="ui-widget-header">Measurement</div>
    <div class="sidebarComponent">
        <form style="text-align:left; font-size: 12px;">
            <table style="font-size:1.0em;">
                <td id="lastonMouseoverObject_kind_b81e5543">-</td> <td id="lastonMouseoverObject_index_b81e5543">-</td> 
                <td id="position_b81e5543">-</td></tr>
            </table>
            <p id="measure_b81e5543"></p>
            <p id="error_b81e5543"></p>
        </form>
    </div>

</div>

<script>
    document.onkeyup = function(e) {
      var x = event.which || event.keyCode;
      var label = 0;
        if (x == 49) {
            set_viewpoint('b81e5543', 'top_pos', 'top_ori');
        } else if (x == 50) {
            set_viewpoint('b81e5543', 'front_pos', 'front_ori');
        } else if (x == 51) {
            set_viewpoint('b81e5543', 'right_pos', 'right_ori');
        } else if (x == 83) {
          spacefilling('b81e5543');
        } else if (x == 66) {
          ballstick('b81e5543');
        } else if (x == 80) {
          polyhedra('b81e5543');
        } else if (x == 52) {
            element('b81e5543');
        } else if (x == 53) {
            index('b81e5543');
        } else if (x == 54) {
            none('b81e5543');
        }
      };
    </script>
 <div class = "column right" > 
   <X3D id = "x3dase" PrimitiveQuality = "high" > 
     <Scene > 
       <Transform id = "t_camera_persp_b81e5543" rotation = "0 0 0 0" > 
         <Viewpoint id = "camera_persp_b81e5543" position = "2.4967547552342126 4.324238904897057 58.14534023565058" centerOfRotation = "2.4967547552342126 4.324238904897057 19.143910284531874" orientation = "0 0 0 0" description = "camera" > 
         </Viewpoint> 
       </Transform> 
       <Transform id = "t_camera_ortho_b81e5543" rotation = "0 0 0 0" > 
         <OrthoViewpoint id = "camera_ortho_b81e5543" position = "2.4967547552342126 4.324238904897057 58.14534023565058" centerOfRotation = "2.4967547552342126 4.324238904897057 19.143910284531874" orientation = "0 0 0 0" fieldOfView = "-13.000476650372901 -13.000476650372901 13.000476650372901 13.000476650372901" description = "camera" > 
         </OrthoViewpoint> 
       </Transform> 
       <Group onclick = "handleGroupClick(event, 'b81e5543')" > 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_C" id = "as_C_b81e5543" > 
             <Appearance DEF = "app_C" > 
               <Material name = "am_b81e5543" diffuseColor = "0.565 0.565 0.565" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_C" radius = "0.76" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_98_b81e5543" uuid = "b81e5543" id = "at_98_b81e5543" radius = "0.76" name = "at_b81e5543" translation = "3.13 5.39 27.18" scale = "1 1 1" > 
           <Shape kind = "C" index = "98" uuid = "b81e5543" > 
             <Appearance USE = "app_C" > 
             </Appearance> 
             <Sphere USE = "asp_C" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_H" id = "as_H_b81e5543" > 
             <Appearance DEF = "app_H" > 
               <Material name = "am_b81e5543" diffuseColor = "1.0 1.0 1.0" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_H" radius = "0.31" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_99_b81e5543" uuid = "b81e5543" id = "at_99_b81e5543" radius = "0.31" name = "at_b81e5543" translation = "4.13 7.08 27.13" scale = "1 1 1" > 
           <Shape kind = "H" index = "99" uuid = "b81e5543" > 
             <Appearance USE = "app_H" > 
             </Appearance> 
             <Sphere USE = "asp_H" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_O" id = "as_O_b81e5543" > 
             <Appearance DEF = "app_O" > 
               <Material name = "am_b81e5543" diffuseColor = "1.0 0.051 0.051" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_O" radius = "0.66" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_96_b81e5543" uuid = "b81e5543" id = "at_96_b81e5543" radius = "0.66" name = "at_b81e5543" translation = "2.62 4.54 26.44" scale = "1 1 1" > 
           <Shape kind = "O" index = "96" uuid = "b81e5543" > 
             <Appearance USE = "app_O" > 
             </Appearance> 
             <Sphere USE = "asp_O" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_97_b81e5543" uuid = "b81e5543" id = "at_97_b81e5543" radius = "0.66" name = "at_b81e5543" translation = "3.75 6.45 26.48" scale = "1 1 1" > 
           <Shape kind = "O" index = "97" uuid = "b81e5543" > 
             <Appearance USE = "app_O" > 
             </Appearance> 
             <Sphere USE = "asp_O" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_Cu" id = "as_Cu_b81e5543" > 
             <Appearance DEF = "app_Cu" > 
               <Material name = "am_b81e5543" diffuseColor = "0.784 0.502 0.2" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_Cu" radius = "1.32" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_0_b81e5543" uuid = "b81e5543" id = "at_0_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-1.3 2.25 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "0" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_1_b81e5543" uuid = "b81e5543" id = "at_1_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-0.0 1.5 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "1" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_2_b81e5543" uuid = "b81e5543" id = "at_2_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "1.3 0.75 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "2" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_3_b81e5543" uuid = "b81e5543" id = "at_3_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "1.29 2.23 24.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "3" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_4_b81e5543" uuid = "b81e5543" id = "at_4_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-0.0 1.5 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "4" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_5_b81e5543" uuid = "b81e5543" id = "at_5_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "1.3 0.75 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "5" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_6_b81e5543" uuid = "b81e5543" id = "at_6_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-2.6 4.5 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "6" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_7_b81e5543" uuid = "b81e5543" id = "at_7_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-1.3 3.75 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "7" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_8_b81e5543" uuid = "b81e5543" id = "at_8_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "0.0 3.0 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "8" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_9_b81e5543" uuid = "b81e5543" id = "at_9_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-0.03 4.5 24.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "9" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_10_b81e5543" uuid = "b81e5543" id = "at_10_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-1.3 3.75 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "10" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_11_b81e5543" uuid = "b81e5543" id = "at_11_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "0.0 3.0 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "11" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_12_b81e5543" uuid = "b81e5543" id = "at_12_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-3.9 6.76 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "12" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_13_b81e5543" uuid = "b81e5543" id = "at_13_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-2.6 6.0 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "13" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_14_b81e5543" uuid = "b81e5543" id = "at_14_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-1.3 5.25 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "14" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_15_b81e5543" uuid = "b81e5543" id = "at_15_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-1.3 6.76 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "15" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_16_b81e5543" uuid = "b81e5543" id = "at_16_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-2.6 6.0 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "16" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_17_b81e5543" uuid = "b81e5543" id = "at_17_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-1.3 5.25 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "17" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_18_b81e5543" uuid = "b81e5543" id = "at_18_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "0.0 0.0 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "18" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_19_b81e5543" uuid = "b81e5543" id = "at_19_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-3.9 8.26 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "19" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_20_b81e5543" uuid = "b81e5543" id = "at_20_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-2.6 7.51 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "20" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_21_b81e5543" uuid = "b81e5543" id = "at_21_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-2.6 9.0 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "21" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_22_b81e5543" uuid = "b81e5543" id = "at_22_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-3.9 8.26 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "22" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_23_b81e5543" uuid = "b81e5543" id = "at_23_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-2.6 7.51 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "23" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_24_b81e5543" uuid = "b81e5543" id = "at_24_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "1.3 2.25 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "24" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_25_b81e5543" uuid = "b81e5543" id = "at_25_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "2.6 1.5 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "25" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_26_b81e5543" uuid = "b81e5543" id = "at_26_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "3.9 0.75 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "26" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_27_b81e5543" uuid = "b81e5543" id = "at_27_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "3.91 2.23 24.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "27" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_28_b81e5543" uuid = "b81e5543" id = "at_28_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "2.6 1.5 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "28" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_29_b81e5543" uuid = "b81e5543" id = "at_29_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "3.9 0.75 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "29" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_30_b81e5543" uuid = "b81e5543" id = "at_30_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "0.0 4.5 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "30" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_31_b81e5543" uuid = "b81e5543" id = "at_31_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "1.3 3.75 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "31" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_32_b81e5543" uuid = "b81e5543" id = "at_32_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "2.6 3.0 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "32" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_33_b81e5543" uuid = "b81e5543" id = "at_33_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "2.59 4.49 24.37" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "33" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_34_b81e5543" uuid = "b81e5543" id = "at_34_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "1.3 3.75 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "34" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_35_b81e5543" uuid = "b81e5543" id = "at_35_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "2.6 3.0 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "35" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_36_b81e5543" uuid = "b81e5543" id = "at_36_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-1.3 6.76 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "36" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_37_b81e5543" uuid = "b81e5543" id = "at_37_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-0.0 6.0 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "37" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_38_b81e5543" uuid = "b81e5543" id = "at_38_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "1.3 5.25 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "38" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_39_b81e5543" uuid = "b81e5543" id = "at_39_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "1.27 6.77 24.28" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "39" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_40_b81e5543" uuid = "b81e5543" id = "at_40_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-0.0 6.0 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "40" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_41_b81e5543" uuid = "b81e5543" id = "at_41_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "1.3 5.25 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "41" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_42_b81e5543" uuid = "b81e5543" id = "at_42_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "2.6 0.0 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "42" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_43_b81e5543" uuid = "b81e5543" id = "at_43_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-1.3 8.26 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "43" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_44_b81e5543" uuid = "b81e5543" id = "at_44_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "0.0 7.51 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "44" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_45_b81e5543" uuid = "b81e5543" id = "at_45_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-0.0 9.01 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "45" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_46_b81e5543" uuid = "b81e5543" id = "at_46_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-1.3 8.26 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "46" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_47_b81e5543" uuid = "b81e5543" id = "at_47_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "0.0 7.51 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "47" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_48_b81e5543" uuid = "b81e5543" id = "at_48_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "3.9 2.25 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "48" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_49_b81e5543" uuid = "b81e5543" id = "at_49_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "5.2 1.5 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "49" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_50_b81e5543" uuid = "b81e5543" id = "at_50_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "6.5 0.75 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "50" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_51_b81e5543" uuid = "b81e5543" id = "at_51_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "6.5 2.25 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "51" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_52_b81e5543" uuid = "b81e5543" id = "at_52_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "5.2 1.5 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "52" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_53_b81e5543" uuid = "b81e5543" id = "at_53_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "6.5 0.75 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "53" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_54_b81e5543" uuid = "b81e5543" id = "at_54_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "2.6 4.5 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "54" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_55_b81e5543" uuid = "b81e5543" id = "at_55_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "3.9 3.75 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "55" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_56_b81e5543" uuid = "b81e5543" id = "at_56_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "5.2 3.0 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "56" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_57_b81e5543" uuid = "b81e5543" id = "at_57_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "5.23 4.49 24.28" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "57" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_58_b81e5543" uuid = "b81e5543" id = "at_58_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "3.9 3.75 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "58" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_59_b81e5543" uuid = "b81e5543" id = "at_59_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "5.2 3.0 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "59" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_60_b81e5543" uuid = "b81e5543" id = "at_60_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "1.3 6.76 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "60" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_61_b81e5543" uuid = "b81e5543" id = "at_61_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "2.6 6.0 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "61" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_62_b81e5543" uuid = "b81e5543" id = "at_62_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "3.9 5.25 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "62" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_63_b81e5543" uuid = "b81e5543" id = "at_63_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "3.91 6.78 24.34" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "63" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_64_b81e5543" uuid = "b81e5543" id = "at_64_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "2.6 6.0 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "64" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_65_b81e5543" uuid = "b81e5543" id = "at_65_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "3.9 5.25 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "65" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_66_b81e5543" uuid = "b81e5543" id = "at_66_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "5.2 0.0 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "66" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_67_b81e5543" uuid = "b81e5543" id = "at_67_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "1.3 8.26 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "67" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_68_b81e5543" uuid = "b81e5543" id = "at_68_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "2.6 7.51 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "68" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_69_b81e5543" uuid = "b81e5543" id = "at_69_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "7.8 0.02 24.3" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "69" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_70_b81e5543" uuid = "b81e5543" id = "at_70_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "1.3 8.26 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "70" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_71_b81e5543" uuid = "b81e5543" id = "at_71_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "2.6 7.51 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "71" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_72_b81e5543" uuid = "b81e5543" id = "at_72_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "6.5 2.25 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "72" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_73_b81e5543" uuid = "b81e5543" id = "at_73_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "7.8 1.5 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "73" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_74_b81e5543" uuid = "b81e5543" id = "at_74_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "9.1 0.75 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "74" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_75_b81e5543" uuid = "b81e5543" id = "at_75_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "9.1 2.25 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "75" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_76_b81e5543" uuid = "b81e5543" id = "at_76_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "7.8 1.5 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "76" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_77_b81e5543" uuid = "b81e5543" id = "at_77_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "9.1 0.75 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "77" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_78_b81e5543" uuid = "b81e5543" id = "at_78_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "5.2 4.5 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "78" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_79_b81e5543" uuid = "b81e5543" id = "at_79_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "6.5 3.75 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "79" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_80_b81e5543" uuid = "b81e5543" id = "at_80_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "7.8 3.0 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "80" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_81_b81e5543" uuid = "b81e5543" id = "at_81_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "7.8 4.5 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "81" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_82_b81e5543" uuid = "b81e5543" id = "at_82_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "6.5 3.75 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "82" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_83_b81e5543" uuid = "b81e5543" id = "at_83_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "7.8 3.0 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "83" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_84_b81e5543" uuid = "b81e5543" id = "at_84_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "3.9 6.76 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "84" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_85_b81e5543" uuid = "b81e5543" id = "at_85_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "5.2 6.0 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "85" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_86_b81e5543" uuid = "b81e5543" id = "at_86_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "6.5 5.25 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "86" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_87_b81e5543" uuid = "b81e5543" id = "at_87_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "-3.88 6.76 24.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "87" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_88_b81e5543" uuid = "b81e5543" id = "at_88_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "5.2 6.0 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "88" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_89_b81e5543" uuid = "b81e5543" id = "at_89_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "6.5 5.25 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "89" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_90_b81e5543" uuid = "b81e5543" id = "at_90_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "7.8 0.0 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "90" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_91_b81e5543" uuid = "b81e5543" id = "at_91_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "3.9 8.26 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "91" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_92_b81e5543" uuid = "b81e5543" id = "at_92_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "5.2 7.51 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "92" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_93_b81e5543" uuid = "b81e5543" id = "at_93_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "0.0 0.01 24.3" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "93" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_94_b81e5543" uuid = "b81e5543" id = "at_94_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "3.9 8.26 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "94" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_95_b81e5543" uuid = "b81e5543" id = "at_95_b81e5543" radius = "1.32" name = "at_b81e5543" translation = "5.2 7.51 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "95" uuid = "b81e5543" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Group> 
       <Shape > 
         <IndexedLineSet coordIndex = "0 1 -1 0 2 -1 0 4 -1 1 3 -1 1 5 -1 2 3 -1 2 6 -1 3 7 -1 4 5 -1 4 6 -1 5 7 -1 6 7 -1" > 
           <Coordinate point = "[[ 0.          0.          0.          0.          0.         38.213406
  -5.2001865   9.00698723  0.         -5.2001865   9.00698723 38.213406
  10.400373    0.          0.         10.400373    0.         38.213406
   5.2001865   9.00698723  0.          5.2001865   9.00698723 38.213406  ]]" > 
           </Coordinate> 
         </IndexedLineSet> 
         <Appearance > 
           <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
           </Material> 
         </Appearance> 
       </Shape> 
       <Switch id = "switch_marker_0_b81e5543" whichChoice = "-1" > 
         <Transform id = "marker_0_b81e5543" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_1_b81e5543" whichChoice = "-1" > 
         <Transform id = "marker_1_b81e5543" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_2_b81e5543" whichChoice = "-1" > 
         <Transform id = "marker_2_b81e5543" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_3_b81e5543" whichChoice = "-1" > 
         <Transform id = "marker_3_b81e5543" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_4_b81e5543" whichChoice = "-1" > 
         <Transform id = "marker_4_b81e5543" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_line_0_b81e5543" whichChoice = "-1" > 
         <Shape > 
           <IndexedLineSet id = "line_ind_0_b81e5543" solid = "false" coordIndex = "0 1 -1" > 
             <Coordinate id = "line_coor_0_b81e5543" point = "0 0 0 0 0 1" > 
             </Coordinate> 
           </IndexedLineSet> 
           <Appearance > 
             <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
             </Material> 
           </Appearance> 
         </Shape> 
       </Switch> 
       <Switch id = "switch_line_1_b81e5543" whichChoice = "-1" > 
         <Shape > 
           <IndexedLineSet id = "line_ind_1_b81e5543" solid = "false" coordIndex = "0 1 -1" > 
             <Coordinate id = "line_coor_1_b81e5543" point = "0 0 0 0 0 1" > 
             </Coordinate> 
           </IndexedLineSet> 
           <Appearance > 
             <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
             </Material> 
           </Appearance> 
         </Shape> 
       </Switch> 
       <TimeSensor DEF = "time" cycleInterval = "10" loop = "true" > 
       </TimeSensor> 
       <PositionInterpolator DEF = "move_0" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 -1.300046625 2.2517468075 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_0" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_0" fromField = "value_changed" toNode = "at_0_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_1" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 -5.200186450014145e-06 1.501167540662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_1" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_1" fromField = "value_changed" toNode = "at_1_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_2" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 1.30005182518645 0.7505792668376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_2" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_2" fromField = "value_changed" toNode = "at_2_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_3" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.2852710112821855 2.228563885615447 24.289538084187463 1.2683468111989464 2.2787317538922642 24.312009625413058 1.2960032202814908 2.2763265377203608 24.34599115203124 1.335747450234409 2.2697375784475025 24.384477985410577 1.2694588290684163 2.3064449593724032 24.302653266550564 1.3264851003667562 2.3158710783465697 24.330698216221464 1.21692353404212 2.3026640435928902 24.364852153941865 1.1297439081527312 2.2923898638245586 24.209204338916503 1.3124216188976634 2.3551037176128804 24.365549162340674 1.301180592647093 2.2508837351609903 24.30350062690284 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_3" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_3" fromField = "value_changed" toNode = "at_3_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_4" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 -5.200186450014145e-06 1.501167540662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_4" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_4" fromField = "value_changed" toNode = "at_4_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_5" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 1.30005182518645 0.7505792668376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_5" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_5" fromField = "value_changed" toNode = "at_5_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_6" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 -2.600093249999999 4.503493614999998 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_6" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_6" fromField = "value_changed" toNode = "at_6_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_7" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 -1.30005182518645 3.7529143481623812 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_7" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_7" fromField = "value_changed" toNode = "at_7_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_8" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 5.200186449903123e-06 3.0023260743376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_8" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_8" fromField = "value_changed" toNode = "at_8_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_9" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.025630852329713474 4.49912024674394 24.290422351474614 -0.016476147072528168 4.60955405109273 24.317755579106496 -0.04959605126205892 4.459592473496541 24.371533372564365 0.0672960224865926 4.391441676383396 24.24876136395359 -0.10970773277978887 4.549868485867269 24.371279078050826 0.13363377999290846 4.616160840230504 24.43989662510661 -0.2235943758283369 4.565821257755694 24.311930336526196 -0.03710571690961917 4.518208876736186 24.214488760904658 -0.019793984471355962 4.47033349715434 24.44094419806192 4.396726601896878e-05 4.506468467617166 24.306850371938935 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_9" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_9" fromField = "value_changed" toNode = "at_9_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_10" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 -1.30005182518645 3.7529143481623812 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_10" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_10" fromField = "value_changed" toNode = "at_10_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_11" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 5.200186449903123e-06 3.0023260743376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_11" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_11" fromField = "value_changed" toNode = "at_11_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_12" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 -3.900139875 6.7552404225 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_12" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_12" fromField = "value_changed" toNode = "at_12_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_13" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 -2.60009845018645 6.004661155662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_13" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_13" fromField = "value_changed" toNode = "at_13_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_14" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 -1.3000414248135508 5.254072881837619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_14" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_14" fromField = "value_changed" toNode = "at_14_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_15" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.3037257019020758 6.756331450177707 24.306625111701425 -1.385031385263753 6.673010144031681 24.337437691248883 -1.1667753870514788 6.733116476155573 24.446044673286664 -1.2810294988523077 6.820809023349274 24.23610736827657 -1.217812346261095 7.022371911278899 24.451771575714357 -1.24093843646866 6.6885954675787005 24.208801561468242 -1.2259379367263519 6.807581683285483 24.192926034367282 -1.3139355292150143 6.8523055984295 24.574127687354068 -1.3008462949363242 6.709028135619249 24.335107156428297 -1.2988237890533971 6.758108395034624 24.308220363006154 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_15" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_15" fromField = "value_changed" toNode = "at_15_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_16" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 -2.60009845018645 6.004661155662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_16" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_16" fromField = "value_changed" toNode = "at_16_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_17" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 -1.3000414248135508 5.254072881837619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_17" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_17" fromField = "value_changed" toNode = "at_17_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_18" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 0.0 0.0 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_18" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_18" fromField = "value_changed" toNode = "at_18_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_19" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 -3.900145075186451 8.256407963162381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_19" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_19" fromField = "value_changed" toNode = "at_19_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_20" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 -2.60008804981355 7.505819689337619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_20" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_20" fromField = "value_changed" toNode = "at_20_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_21" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.6003048644486473 9.003735166366159 24.305822535654695 -2.5944160997821077 9.034878010651891 24.499481814129908 -2.5971189125291163 9.20821458168344 24.50289122867834 -2.3280432471422263 9.192497467413268 24.48999690337507 -2.7228176914478546 9.041465072279602 25.362275950715738 -2.800971786866429 7.176045462775747 26.333720902725283 -1.5623433376711398 6.060926859707732 26.346618636172607 0.2709688107197698 5.314453040122142 26.353779875762825 1.7751640677205671 3.6692439239301824 26.134609747998063 2.597052949973667 0.004637914595010559 24.300787133478703 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_21" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_21" fromField = "value_changed" toNode = "at_21_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_22" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 -3.900145075186451 8.256407963162381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_22" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_22" fromField = "value_changed" toNode = "at_22_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_23" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 -2.60008804981355 7.505819689337619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_23" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_23" fromField = "value_changed" toNode = "at_23_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_24" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 1.3000466249999996 2.2517468075 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_24" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_24" fromField = "value_changed" toNode = "at_24_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_25" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 2.60008804981355 1.501167540662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_25" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_25" fromField = "value_changed" toNode = "at_25_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_26" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 3.9001450751864506 0.7505792668376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_26" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_26" fromField = "value_changed" toNode = "at_26_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_27" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.912710644692814 2.229280869141756 24.290492977199953 3.8507830789568764 2.2710416416667205 24.375568534832738 3.8279401622929528 2.299262032018505 24.39734320335591 3.9161018063418807 2.380789155687831 24.41191535577188 3.955823611539432 2.4041131847899595 24.44455660846741 3.910328293981252 2.446440370276792 24.415998236401148 3.9986085369479447 2.305170357069459 24.39098344980749 3.75597587888495 2.3721699621457843 24.362487825734657 3.912924790908396 2.4260678014037245 24.432738498756816 3.900942778374182 2.2539099098207926 24.307030382651106 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_27" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_27" fromField = "value_changed" toNode = "at_27_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_28" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 2.60008804981355 1.501167540662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_28" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_28" fromField = "value_changed" toNode = "at_28_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_29" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 3.9001450751864506 0.7505792668376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_29" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_29" fromField = "value_changed" toNode = "at_29_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_30" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 4.219158078466734e-16 4.503493614999998 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_30" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_30" fromField = "value_changed" toNode = "at_30_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_31" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 1.3000414248135501 3.7529143481623812 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_31" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_31" fromField = "value_changed" toNode = "at_31_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_32" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 2.6000984501864504 3.0023260743376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_32" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_32" fromField = "value_changed" toNode = "at_32_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_33" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.5879023741143854 4.486597847722434 24.370256456901597 2.5000388224629524 4.623583316755999 24.256669742658005 2.5649841677028253 4.56310960296535 24.327132672485394 2.5976344169524817 4.525265284718661 24.356392794293892 2.488496640427074 4.568196116543138 24.519139375972298 2.620930229100623 4.622792299588997 24.367628967409427 2.598053321216821 4.580505290373852 24.4636947418645 2.6002775326607575 4.631711012694382 24.4943630294438 2.660329906696275 4.7743218553039055 24.278203670905405 2.605055359344784 4.5076093063515295 24.288446971856395 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_33" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_33" fromField = "value_changed" toNode = "at_33_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_34" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 1.3000414248135501 3.7529143481623812 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_34" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_34" fromField = "value_changed" toNode = "at_34_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_35" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 2.6000984501864504 3.0023260743376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_35" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_35" fromField = "value_changed" toNode = "at_35_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_36" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 -1.3000466250000005 6.7552404225 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_36" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_36" fromField = "value_changed" toNode = "at_36_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_37" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 -5.200186449903123e-06 6.004661155662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_37" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_37" fromField = "value_changed" toNode = "at_37_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_38" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 1.3000518251864503 5.254072881837619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_38" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_38" fromField = "value_changed" toNode = "at_38_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_39" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.27240363623041 6.770457952058309 24.27887620453943 1.3174189514413857 6.894964734612965 24.339105512104663 1.3612450908750306 6.896059434628504 24.391825061412252 1.3197919428274123 6.674432840290282 24.260155095450216 1.3001558904209363 6.94223103499042 24.395183683415958 1.3318406272132721 6.862427207554795 24.32336920287467 1.2568760809138229 6.723570815106251 24.433014358241657 1.2453816746080033 6.6768163105924785 24.539573542059042 1.2061287605356807 6.853229885768586 24.33171032325352 1.2825792438615928 6.744289769801695 24.298438396343236 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_39" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_39" fromField = "value_changed" toNode = "at_39_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_40" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 -5.200186449903123e-06 6.004661155662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_40" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_40" fromField = "value_changed" toNode = "at_40_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_41" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 1.3000518251864503 5.254072881837619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_41" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_41" fromField = "value_changed" toNode = "at_41_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_42" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 2.6000932499999996 0.0 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_42" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_42" fromField = "value_changed" toNode = "at_42_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_43" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 -1.30005182518645 8.256407963162381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_43" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_43" fromField = "value_changed" toNode = "at_43_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_44" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 5.200186450347212e-06 7.505819689337619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_44" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_44" fromField = "value_changed" toNode = "at_44_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_45" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.0002248425801898188 9.006066033303107 24.306802193629586 -0.01070929456447705 8.920225364243636 24.386854587483384 -0.048734281237957866 9.161333094817609 24.266630395473378 0.014734785054849526 8.09136714652111 25.487359917442063 -0.27685400221850764 8.33419613511808 26.34370317623701 -0.27192632484326273 7.0432039523323455 26.311072228154984 0.7946069756010303 5.286289160745613 26.35594760592562 1.8838095786744857 3.2181351007611605 26.38787672894353 3.314621297512426 1.437393127446129 26.443151272960492 5.204225038028712 0.0020967613865172517 24.300809667400795 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_45" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_45" fromField = "value_changed" toNode = "at_45_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_46" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 -1.30005182518645 8.256407963162381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_46" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_46" fromField = "value_changed" toNode = "at_46_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_47" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 5.200186450347212e-06 7.505819689337619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_47" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_47" fromField = "value_changed" toNode = "at_47_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_48" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 3.900139874999998 2.2517468075 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_48" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_48" fromField = "value_changed" toNode = "at_48_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_49" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 5.200181299813549 1.501167540662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_49" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_49" fromField = "value_changed" toNode = "at_49_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_50" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 6.50023832518645 0.7505792668376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_50" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_50" fromField = "value_changed" toNode = "at_50_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_51" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.5044053586658475 2.2481143406561883 24.30760221933834 6.319897080812097 2.421905875133435 24.428717526265775 6.465775767429231 2.351013172987377 24.278341298138102 6.585031745449049 2.415403566766385 24.228093001245167 6.6336204294250605 2.541194962317088 24.384014002950476 6.481974836754424 2.2935313842550764 24.346218836502185 6.527987492322527 2.308958057711291 24.466423995194283 6.508241549804155 2.358523734536818 24.558738283152962 6.315639512592642 2.2583662043042816 24.3586128484695 6.501372104304568 2.24980663763322 24.301059724506434 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_51" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_51" fromField = "value_changed" toNode = "at_51_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_52" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 5.200181299813549 1.501167540662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_52" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_52" fromField = "value_changed" toNode = "at_52_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_53" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 6.50023832518645 0.7505792668376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_53" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_53" fromField = "value_changed" toNode = "at_53_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_54" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 2.600093249999999 4.503493614999998 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_54" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_54" fromField = "value_changed" toNode = "at_54_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_55" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 3.9001346748135486 3.7529143481623812 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_55" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_55" fromField = "value_changed" toNode = "at_55_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_56" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 5.20019170018645 3.0023260743376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_56" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_56" fromField = "value_changed" toNode = "at_56_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_57" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.229104512232149 4.485467560920783 24.277609411241063 5.198883467133217 4.6032362239249185 24.360583918960305 5.153574139960717 4.489239175947109 24.366401410123775 5.231925275343914 4.593263407209485 24.36124855062981 5.154704407405797 4.6857988281929615 24.37584324527977 5.252160315624894 4.611888571476191 24.39015005586322 5.233507849080222 4.609063021123076 24.367683551991526 5.265279397543885 4.515854873269796 24.374477811005537 5.217135610788723 4.644544377177985 24.39068496465696 5.196047674219077 4.505617864801554 24.30118230044136 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_57" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_57" fromField = "value_changed" toNode = "at_57_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_58" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 3.9001346748135486 3.7529143481623812 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_58" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_58" fromField = "value_changed" toNode = "at_58_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_59" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 5.20019170018645 3.0023260743376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_59" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_59" fromField = "value_changed" toNode = "at_59_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_60" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 1.3000466249999982 6.7552404225 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_60" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_60" fromField = "value_changed" toNode = "at_60_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_61" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 2.600088049813549 6.004661155662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_61" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_61" fromField = "value_changed" toNode = "at_61_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_62" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 3.90014507518645 5.254072881837619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_62" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_62" fromField = "value_changed" toNode = "at_62_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_63" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.910902287092594 6.775543367105439 24.342124225395853 3.777437344242156 6.853933937393087 24.501268004419703 3.8051031172147507 6.904996031837308 24.460188134108133 3.92448605258513 6.879972439239787 24.33125012800268 3.903529057266768 6.857553046416372 24.442513168527483 3.890282995273107 6.829492990689578 24.481690258169365 3.9187038217663157 6.857722285746535 24.49640041803901 3.887909300850277 6.8453044761169295 24.478702048877228 3.8736969110915154 6.8306599560307495 24.456446822314284 3.9005054923431444 6.755503393567644 24.34700213991426 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_63" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_63" fromField = "value_changed" toNode = "at_63_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_64" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 2.600088049813549 6.004661155662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_64" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_64" fromField = "value_changed" toNode = "at_64_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_65" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 3.90014507518645 5.254072881837619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_65" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_65" fromField = "value_changed" toNode = "at_65_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_66" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 5.200186499999998 0.0 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_66" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_66" fromField = "value_changed" toNode = "at_66_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_67" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 1.3000414248135488 8.256407963162381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_67" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_67" fromField = "value_changed" toNode = "at_67_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_68" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 2.60009845018645 7.505819689337619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_68" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_68" fromField = "value_changed" toNode = "at_68_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_69" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "7.796133286659032 0.017411357712105887 24.29530482713749 2.6410962341156585 9.304943479279482 24.501842043478412 2.7345747669956886 9.20918968278771 24.647249822307867 2.512452640008807 9.023190419075561 24.51940817077013 7.846668373428218 0.106870946800891 24.470793205868173 7.77995706326216 0.046081607239234185 24.433456028141162 7.820541267821799 0.09712378973004122 24.511221578651487 7.669032370989408 -0.059730943909451495 24.646059967850626 7.8537708981921 0.04493827435769807 24.324262953117913 7.799899091190856 0.0007915587399410709 24.344030930140484 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_69" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_69" fromField = "value_changed" toNode = "at_69_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_70" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 1.3000414248135488 8.256407963162381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_70" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_70" fromField = "value_changed" toNode = "at_70_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_71" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 2.60009845018645 7.505819689337619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_71" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_71" fromField = "value_changed" toNode = "at_71_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_72" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 6.500233124999998 2.2517468075 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_72" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_72" fromField = "value_changed" toNode = "at_72_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_73" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 7.80027454981355 1.501167540662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_73" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_73" fromField = "value_changed" toNode = "at_73_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_74" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 9.10033157518645 0.7505792668376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_74" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_74" fromField = "value_changed" toNode = "at_74_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_75" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "9.097014909158794 2.250018707423952 24.307413527985673 7.952720517356431 2.288879230638959 26.2008914952053 7.290244610870761 2.014322782694783 26.417963070309458 5.664567874918434 1.259877339441556 25.9851269328703 5.37998425169143 0.5663028148369172 24.59038776675011 5.39922995564615 0.005741981916702426 24.484874168767636 2.9277458592172834 0.09674283322417945 24.435405968990445 2.5731042034622567 0.22369968668570894 24.74589318376932 2.830171220815822 0.010468433046072395 24.299339514075974 -1.298826719394734 2.2749287609590314 24.29844025211366 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_75" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_75" fromField = "value_changed" toNode = "at_75_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_76" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 7.80027454981355 1.501167540662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_76" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_76" fromField = "value_changed" toNode = "at_76_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_77" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 9.10033157518645 0.7505792668376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_77" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_77" fromField = "value_changed" toNode = "at_77_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_78" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 5.200186499999998 4.503493614999998 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_78" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_78" fromField = "value_changed" toNode = "at_78_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_79" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 6.50022792481355 3.7529143481623812 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_79" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_79" fromField = "value_changed" toNode = "at_79_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_80" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 7.80028495018645 3.0023260743376188 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_80" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_80" fromField = "value_changed" toNode = "at_80_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_81" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "7.800704187785526 4.502124815090319 24.307837212709597 7.839533847806593 4.4529438698625 24.48263074831934 7.7584202370638 4.506296739251183 24.429071939174644 7.839889512143031 4.684191407396478 24.420117033536297 7.743862184092075 4.690903861417513 24.261193907422996 7.667708356431725 4.5634280661433575 24.423454937934306 7.785342126072894 4.5507430677136025 24.463313678729868 -2.5708685747202695 4.556995893796812 24.392966869666555 -2.6289086206432253 4.527399977122188 24.36082532790192 7.799157678439427 4.504027136738051 24.307218527193186 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_81" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_81" fromField = "value_changed" toNode = "at_81_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_82" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 6.50022792481355 3.7529143481623812 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_82" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_82" fromField = "value_changed" toNode = "at_82_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_83" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 7.80028495018645 3.0023260743376188 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_83" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_83" fromField = "value_changed" toNode = "at_83_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_84" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 3.900139874999998 6.7552404225 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_84" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_84" fromField = "value_changed" toNode = "at_84_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_85" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 5.20018129981355 6.004661155662381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_85" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_85" fromField = "value_changed" toNode = "at_85_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_86" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 6.50023832518645 5.254072881837619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_86" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_86" fromField = "value_changed" toNode = "at_86_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_87" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.8825680011940267 6.757014070311783 24.29459035395583 -3.8967334453228375 6.756194466323662 24.379241604909666 -3.9129094183874926 6.63622519529885 24.378364792795985 -3.908103355259819 6.885534102838949 24.313399083525958 -3.8539143651128693 6.944717009946018 24.515412258897697 -3.9514566127510804 6.845834355356391 24.325885507542488 -3.8041698299041316 6.839245803046959 24.42802252671151 -3.9089986696957957 6.882415934860697 24.418253559403876 -3.7609066188332156 6.818427389719923 24.37745161388732 -3.8810194214810325 6.746315807575217 24.297723338610304 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_87" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_87" fromField = "value_changed" toNode = "at_87_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_88" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 5.20018129981355 6.004661155662381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_88" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_88" fromField = "value_changed" toNode = "at_88_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_89" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 6.50023832518645 5.254072881837619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_89" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_89" fromField = "value_changed" toNode = "at_89_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_90" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 7.800279749999998 0.0 18.04521100813208 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_90" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_90" fromField = "value_changed" toNode = "at_90_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_91" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 3.90013467481355 8.256407963162381 13.799281254066045 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_91" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_91" fromField = "value_changed" toNode = "at_91_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_92" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 5.2001917001864495 7.505819689337619 15.922265237801877 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_92" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_92" fromField = "value_changed" toNode = "at_92_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_93" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.0043635330345542025 0.008559828607489805 24.297833334834586 0.02990358426749443 0.032427579960623905 24.309802626975102 0.0223272057256109 0.011640302394694555 24.43703584266093 -5.212288359962886 9.083302101361422 24.469188508217982 -5.219637413816869 9.07350711092112 24.47721583250706 5.183608871245449 9.003877070662677 24.37342327137681 5.273923689638891 9.001383807231054 24.454929617398616 5.176945935098408 9.045190514329914 24.389125113029632 5.225045952726908 8.907181553575889 24.416989001157894 -1.4000982320326613e-05 0.0024914315676172745 24.346629549542026 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_93" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_93" fromField = "value_changed" toNode = "at_93_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_94" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 3.90013467481355 8.256407963162381 20.168194991867917 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_94" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_94" fromField = "value_changed" toNode = "at_94_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_95" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 5.2001917001864495 7.505819689337619 22.291140762198122 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_95" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_95" fromField = "value_changed" toNode = "at_95_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_96" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.624578109042964 4.544935320212249 26.438483253732468 2.3988119842832907 4.150126541669041 26.86712781645553 2.4348617120029874 4.515881823884571 27.061743154226317 2.4843339434537346 4.529704874893619 27.51646483876877 2.646244906116837 4.275344237311929 27.778052320841102 2.9088013477645913 3.9399351484148557 28.460928068671866 2.970795877968116 4.077047895546991 29.095566943957607 2.3519118412233397 4.5277190324394425 29.372333877015343 1.8524829293703355 4.924699163368566 29.46626591485077 1.3860358214770436 3.575080445641074 27.757995549739015 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_96" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_96" fromField = "value_changed" toNode = "at_96_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_97" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.7520521804625546 6.453228960744443 26.476685452320268 4.411286240599862 6.659102043306677 26.226056435082256 4.318961808229797 5.974513050613607 26.958100628329433 4.034729811239952 6.238391251619962 26.985335049580055 3.681058800646844 6.306524788718283 27.149903523376047 4.02124437905826 5.98333291127926 28.848117702606487 4.113597365502469 6.145986747870435 29.223109904673795 3.8781435749556743 6.291484513282825 29.02610197702859 3.6318242704594375 6.452723616962588 29.18880029241318 2.5767486186834434 5.604993826724348 27.706600094000084 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_97" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_97" fromField = "value_changed" toNode = "at_97_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_98" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.1255048000722034 5.3913014668806865 27.18237094006607 2.923954119739729 4.829273209517995 27.632438174465022 3.4064053622765664 5.202251483902111 27.126542911651768 3.233159436382438 5.383867044933519 27.212554450870655 3.206321144882848 5.259243285388933 27.431892799772218 3.455564288097765 4.960642439003321 28.68053739794085 3.5263696970806255 5.117423477626017 29.16851784849982 3.1222141159717913 5.4036322665541086 29.174550055715 2.747231847703932 5.679148215220699 29.295986793835922 1.981417330527889 4.589936520963488 27.729831888455397 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_98" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_98" fromField = "value_changed" toNode = "at_98_b81e5543" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_99" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.125591223648816 7.081860309273324 27.127527904912807 4.14172723870757 6.755277148019773 27.144269712525375 3.52350630996536 7.363924893006636 26.07286416892462 3.67320887235095 8.12378824826948 25.526103065913567 3.794427746328471 8.451894683252846 25.44260703741689 3.8860156949832905 8.309210130345388 25.381424513995885 3.9088651115148316 8.246348150124469 25.401925847431823 3.858905506103791 8.284257233338927 25.385504988932823 3.864189079988632 8.35069899470905 25.42002900891896 3.9012618588625823 8.261142839433239 25.23474031697266 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_99" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_99" fromField = "value_changed" toNode = "at_99_b81e5543" toField = "translation" > 
       </ROUTE> 
     </Scene> 
   </X3D> 
 </div> 

<script type="text/javascript" src="https://code.jquery.com/jquery-2.1.0.min.js" ></script>
 <script >
 if (atoms_dict == undefined) {var atoms_dict = {"new": true};}; 
atoms_dict["b81e5543"] = {com: "2.50 4.32 19.14", 
top_pos: "2.50 4.32 58.15", 
front_pos: "2.50 -34.68 19.14", 
right_pos: "41.50 4.32 19.14", 
left_pos: "-36.50 4.32 19.14", 
top_ori: "0 0 0 0", 
front_ori: "1 0 0 1.57079", 
right_ori: "0 1 0 1.57079", 
left_ori: "0 1 0 -1.57079", 
select: [], 
uuid: "b81e5543", 
label: "False", 
bond: "False", 
polyhedra: {}, 
};

// if (atoms_dict[uuid]['new']){
function setQuality(uuid, quality){
        // $('#error_'.concat(atoms_dict[uuid]['uuid'])).html('uuid: '.concat(atoms_dict[uuid]["uuid"]));
        var x3d = 'x3dase_' + uuid;
        document.getElementById(x3d).setAttribute('PrimitiveQuality', quality);
    }
function set_viewpoint(uuid, pos, ori){
    // $('#error_'.concat(atoms_dict[uuid]['uuid'])).html('uuid: '.concat(atoms_dict[uuid]["uuid"]));
    var persp = 'camera_persp_' + uuid;
    var ortho = 'camera_ortho_' + uuid;
    document.getElementById(persp).setAttribute('orientation', atoms_dict[uuid][ori]);
    document.getElementById(persp).setAttribute('position', atoms_dict[uuid][pos]);
    document.getElementById(ortho).setAttribute('orientation', atoms_dict[uuid][ori]);
    document.getElementById(ortho).setAttribute('position', atoms_dict[uuid][pos]);
}

//Round a float value to x.xx format
function roundWithTwoDecimals(value)
{
    var x = (Math.round(value * 100)) / 100;
    var y = x.toFixed(2);
    return y;
}
//Handle click on any group member
function handleGroupClick(event)
{
    //Mark hitting point
    var target = event.target;
    var uuid = target.parentNode.getAttribute('uuid')
    var radius = target.parentNode.getAttribute('radius');
    var scale = target.parentNode.getAttribute('scale');
    scale = parseFloat(radius)*parseFloat(scale)*1.2;
    var scale = ' ' + scale + ' ' + scale + ' ' + scale;
    var translation = target.parentNode.getAttribute('translation');
    var id = target.parentNode.getAttribute('id');
    if (window.event.ctrlKey) {
        atoms_dict[uuid]['select'].push(id);

    }
    else {
        for (var i=1; i<= atoms_dict[uuid]['select'].length; i++) {
            $('#switch_marker_' + i + '_' + uuid).attr('whichChoice', -1);
        }
        atoms_dict[uuid]['select'] = [];
        atoms_dict[uuid]['select'].push(id);
        $('#switch_marker_' + 2 + '_' + uuid).attr('whichChoice', -1);
}
    var n = atoms_dict[uuid]['select'].length;
    $('#switch_marker_' + n + '_' + uuid).attr('whichChoice', 0);
    $('#marker_' + n + '_' + uuid).attr('translation', translation);
    $('#marker_' + n + '_' + uuid).attr('scale', scale);
    var atom_kind = '#lastonMouseoverObject_kind_'.concat(uuid);
    var atom_index = '#lastonMouseoverObject_index_'.concat(uuid);
    $(atom_kind).html(target.getAttribute("kind"));
    $(atom_index).html(target.getAttribute("index"));
    //
    var coord = translation.split(" ")
    var atom_position = '#position_'.concat(uuid);
    var x = roundWithTwoDecimals(coord[0]);
    var y = roundWithTwoDecimals(coord[1]);
    var z = roundWithTwoDecimals(coord[2]);
    var position = 'x = ' + x + ' y = ' + y + ' z = ' + z;
    $(atom_position).html(position);

    if (atoms_dict[uuid]['select'].length == 2){
        calculate_distance(uuid);
        draw_line(uuid);
    }
    else if (atoms_dict[uuid]['select'].length == 3){
        calculate_angle(uuid);
        draw_line(uuid);
    }

    console.log(event);
}
//Add a onMouseover callback to every shape
$(document).ready(function(){
    $("shape").each(function() {
        // $(this).attr("onMouseover", "handleOnMouseover_shape(this)");
        // $(this).attr("onclick", "handleClick_shape(this)");
    });
    //Add a onMouseover callback to every transform
    $("transform").each(function() {
        // $(this).attr("onMouseover", "handleOnMouseover_transform(this)");
        // $(this).attr("onclick", "handleClick_transform(this)");
    });
});
$(document).on("click", function(e) {
    if (e.target === document || e.target.tagName === "BODY" || e.target.tagName === "HTML") {
        $('#marker').attr('scale', "0.0001 0.0001 0.0001");
    }
});
//Handle onMouseover on a shape
function handleOnMouseover_shape(shape)
{
    var atom_kind = '#lastonMouseoverObject_kind_'.concat($(shape).attr("uuid"));
    var atom_index = '#lastonMouseoverObject_index_'.concat($(shape).attr("uuid"));
    $(atom_kind).html($(shape).attr("kind"));
    $(atom_index).html($(shape).attr("index"));
}
//Handle onMouseover on a shape
function handleClick_shape(shape)
{
    var atom_kind = '#lastonMouseoverObject_kind_'.concat($(shape).attr("uuid"));
    var atom_index = '#lastonMouseoverObject_index_'.concat($(shape).attr("uuid"));
    $(atom_kind).html($(shape).attr("kind"));
    $(atom_index).html($(shape).attr("index"));
}
//Handle onMouseover on a transform
function handleOnMouseover_transform(transform)
{
    var atom_position = '#position_'.concat($(transform).attr("uuid"));
    var coord = $(transform).attr("translation").split(" "[0]);
    var x = roundWithTwoDecimals(coord[0]);
    var y = roundWithTwoDecimals(coord[1]);
    var z = roundWithTwoDecimals(coord[2]);
    var position = 'x = ' + x + ' y = ' + y + ' z = ' + z;
    $(atom_position).html(position);
}

function calculate_distance(uuid)
{
    var measure = '#measure_'.concat(uuid);
    var c1 = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation").split(" ");
    var c2 = document.getElementById(atoms_dict[uuid]['select'][1]).getAttribute("translation").split(" ");
    r = (c1[0] - c2[0])*(c1[0] - c2[0]) + (c1[1] - c2[1])*(c1[1] - c2[1]) + (c1[2] - c2[2])*(c1[2] - c2[2]);
    r = roundWithTwoDecimals(Math.sqrt(r));
    var dist = 'Distance:  ' + r;
    $(measure).html(dist);
}
function calculate_angle(uuid)
{
    var measure = '#measure_'.concat(uuid);
    var c1 = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation").split(" ");
    var c2 = document.getElementById(atoms_dict[uuid]['select'][1]).getAttribute("translation").split(" ");
    var c3 = document.getElementById(atoms_dict[uuid]['select'][2]).getAttribute("translation").split(" ");
    var AB = Math.sqrt(Math.pow(c2[0]-c1[0],2)+ Math.pow(c2[1]-c1[1],2) + Math.pow(c2[2]-c1[2],2));    
    var BC = Math.sqrt(Math.pow(c2[0]-c3[0],2)+ Math.pow(c2[1]-c3[1],2) + Math.pow(c2[2]-c3[2],2)); 
    var AC = Math.sqrt(Math.pow(c3[0]-c1[0],2)+ Math.pow(c3[1]-c1[1],2)+ Math.pow(c3[2]-c1[2],2));
    var angle = roundWithTwoDecimals(Math.acos((BC*BC+AB*AB-AC*AC)/(2*BC*AB))*180/3.1415926);
    var angle = 'angle:  ' + angle;
    $(measure).html(angle);
}
function draw_line(uuid)
{
    var n = atoms_dict[uuid]['select'].length;
    var coordIndex = '';
    var point = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation");
    for (var i = 1; i < n; i++) {
        var c1 = document.getElementById(atoms_dict[uuid]['select'][i]).getAttribute("translation");
        coordIndex = coordIndex + (i-1) + ' ' + i + ' -1 ';
        point = point + ' ' + c1 + ' ';
    }
    $('#line_coor_' + 0 + '_' + uuid).attr('point', point);
    $('#line_ind_' + 0 + '_' + uuid).attr('coordIndex', coordIndex);
    $('#switch_line_' + 0 + '_' + uuid).attr('whichChoice', 0);
}
//Handle models
function spacefilling(uuid)
{
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "1.0, 1.0, 1.0");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function ballstick(uuid)
{
    if (atoms_dict[uuid]['bond']=='False'){ 
        alert('Please set bond parameter in your code, e.g. bond=1.0!');
        $('#error_'.concat(uuid)).html('(^_^) Please set bond parameter in your code, e.g. bond=1.0!');
		return ;
    }
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "0.6, 0.6, 0.6");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function polyhedra(uuid)
{
    if (atoms_dict[uuid]['polyhedra'].length==0){ 
        alert('Please set polyhedra parameter in your code, e.g. polyhedra={"Ti": ["O"]}!');
        $('#error_'.concat(uuid)).html('(^_^) Please set polyhedra parameter in your code, e.g. polyhedra={"Ti": ["O"]}!');
		return ;
    }
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "0.6, 0.6, 0.6");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '0');
}
function none(uuid)
{
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.0");
        }
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
        
function element(uuid)
{
    if (atoms_dict[uuid]['label']=='False'){ 
        alert('To show element, please set label=True in your code!');
        $('#error_'.concat(uuid)).html('(^_^) To show element, please set label=True in your code!');
		return ;
	}
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.4");
        }
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function index(uuid)
{
    if (atoms_dict[uuid]['label']=='False'){ 
        alert('To show index, please set label=True in your code!');
        $('#error_'.concat(uuid)).html('(^_^) To show index, please set label=True in your code!');
		return ;
	}
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.4");
        }
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
// } 
</script> </body>
</html>
