<html>
<head>
<title>ASE atomic visualization</title>
<link rel="stylesheet" type="text/css"
 href="https://www.x3dom.org/x3dom/release/x3dom.css">
</link>
<script type="text/javascript"
 src="https://www.x3dom.org/x3dom/release/x3dom.js">
</script>
<style>
* {
    box-sizing: border-box;
  }
 
/* Create two unequal columns that floats next to each other */
.column {
  float: left;
  padding: 1px;
}

.left {
  width: 20%;
}

.right {
  width: 80%;
}

/* Clear floats after the columns */
.row:after {
  content: "";
  display: table;
  clear: both;
}

#x3dase{
    top:0;
    width: 80%;
    height: 80%;
    border:2px solid darkorange;        
}

#sidebar{
    top:0;
    border:2px solid darkorange;        
}


/* Sidebar component containers */
.ui-widget-header
{
  background-color: lightblue;
  font-size: 12px;

}
.sidebarComponent
{
    padding:2px 2px 2px 2px;
    font-size: medium;
}

.button {
  background-color: #4CAF50; /* Green */
  border: 1px solid green;
  color: white;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 10px;
  cursor: pointer;
  /* float: left; */
}

</style></head>
<body>
<div class = "column left", id = "sidebar">

    <div class="ui-widget-header">Model</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="spacefilling('e96fc17d')">Ball</button>
            <button type="button" class = "button" onclick="ballstick('e96fc17d')">Ball-and-stick</button>
            <button type="button" class = "button" onclick="polyhedra('e96fc17d')">Polyhedra</button>
        </form>
    </div>
    <div class="ui-widget-header">Label</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="none('e96fc17d')">None</button>
            <button type="button" class = "button" onclick="element('e96fc17d')"> Element</button>
            <button type="button" class = "button" onclick="index('e96fc17d')">Index</button>
        </form>
    </div>

    <div class="ui-widget-header">Camera</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="document.getElementById('camera_ortho_e96fc17d').setAttribute('set_bind','true');">Orthographic</button>
            <button type="button" class = "button" onclick="document.getElementById('camera_persp_e96fc17d').setAttribute('set_bind','true');">Perspective</button>
        </form>
    </div>

    <div class="ui-widget-header">View</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="set_viewpoint('e96fc17d', 'top_pos', 'top_ori')">Top</button>
            <button type="button" class = "button" onclick="set_viewpoint('e96fc17d', 'front_pos', 'front_ori')">Front</button>
            <button type="button" class = "button" onclick="set_viewpoint('e96fc17d', 'right_pos', 'right_ori')">Right</button>
        </form>
    </div>

    <div class="ui-widget-header">Measurement</div>
    <div class="sidebarComponent">
        <form style="text-align:left; font-size: 12px;">
            <table style="font-size:1.0em;">
                <td id="lastonMouseoverObject_kind_e96fc17d">-</td> <td id="lastonMouseoverObject_index_e96fc17d">-</td> 
                <td id="position_e96fc17d">-</td></tr>
            </table>
            <p id="measure_e96fc17d"></p>
            <p id="error_e96fc17d"></p>
        </form>
    </div>

</div>

<script>
    document.onkeyup = function(e) {
      var x = event.which || event.keyCode;
      var label = 0;
        if (x == 49) {
            set_viewpoint('e96fc17d', 'top_pos', 'top_ori');
        } else if (x == 50) {
            set_viewpoint('e96fc17d', 'front_pos', 'front_ori');
        } else if (x == 51) {
            set_viewpoint('e96fc17d', 'right_pos', 'right_ori');
        } else if (x == 83) {
          spacefilling('e96fc17d');
        } else if (x == 66) {
          ballstick('e96fc17d');
        } else if (x == 80) {
          polyhedra('e96fc17d');
        } else if (x == 52) {
            element('e96fc17d');
        } else if (x == 53) {
            index('e96fc17d');
        } else if (x == 54) {
            none('e96fc17d');
        }
      };
    </script>
 <div class = "column right" > 
   <X3D id = "x3dase" PrimitiveQuality = "high" > 
     <Scene > 
       <Transform id = "t_camera_persp_e96fc17d" rotation = "0 0 0 0" > 
         <Viewpoint id = "camera_persp_e96fc17d" position = "2.7031119638018093 4.688284535792894 50.946590819762534" centerOfRotation = "2.7031119638018093 4.688284535792894 11.945195173173312" orientation = "0 0 0 0" description = "camera" > 
         </Viewpoint> 
       </Transform> 
       <Transform id = "t_camera_ortho_e96fc17d" rotation = "0 0 0 0" > 
         <OrthoViewpoint id = "camera_ortho_e96fc17d" position = "2.7031119638018093 4.688284535792894 50.946590819762534" centerOfRotation = "2.7031119638018093 4.688284535792894 11.945195173173312" orientation = "0 0 0 0" fieldOfView = "-13.000465215529742 -13.000465215529742 13.000465215529742 13.000465215529742" description = "camera" > 
         </OrthoViewpoint> 
       </Transform> 
       <Group onclick = "handleGroupClick(event, 'e96fc17d')" > 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_H" id = "as_H_e96fc17d" > 
             <Appearance DEF = "app_H" > 
               <Material name = "am_e96fc17d" diffuseColor = "1.0 1.0 1.0" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_H" radius = "0.31" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_99_e96fc17d" uuid = "e96fc17d" id = "at_99_e96fc17d" radius = "0.31" name = "at_e96fc17d" translation = "2.74 4.71 21.0" scale = "1 1 1" > 
           <Shape kind = "H" index = "99" uuid = "e96fc17d" > 
             <Appearance USE = "app_H" > 
             </Appearance> 
             <Sphere USE = "asp_H" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_O" id = "as_O_e96fc17d" > 
             <Appearance DEF = "app_O" > 
               <Material name = "am_e96fc17d" diffuseColor = "1.0 0.051 0.051" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_O" radius = "0.66" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_96_e96fc17d" uuid = "e96fc17d" id = "at_96_e96fc17d" radius = "0.66" name = "at_e96fc17d" translation = "3.04 5.2 20.19" scale = "1 1 1" > 
           <Shape kind = "O" index = "96" uuid = "e96fc17d" > 
             <Appearance USE = "app_O" > 
             </Appearance> 
             <Sphere USE = "asp_O" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_97_e96fc17d" uuid = "e96fc17d" id = "at_97_e96fc17d" radius = "0.66" name = "at_e96fc17d" translation = "1.95 3.44 19.31" scale = "1 1 1" > 
           <Shape kind = "O" index = "97" uuid = "e96fc17d" > 
             <Appearance USE = "app_O" > 
             </Appearance> 
             <Sphere USE = "asp_O" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_Cu" id = "as_Cu_e96fc17d" > 
             <Appearance DEF = "app_Cu" > 
               <Material name = "am_e96fc17d" diffuseColor = "0.784 0.502 0.2" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_Cu" radius = "1.32" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_0_e96fc17d" uuid = "e96fc17d" id = "at_0_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "1.62 2.81 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "0" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_1_e96fc17d" uuid = "e96fc17d" id = "at_1_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "2.92 2.06 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "1" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_2_e96fc17d" uuid = "e96fc17d" id = "at_2_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "4.22 1.31 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "2" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_3_e96fc17d" uuid = "e96fc17d" id = "at_3_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "4.26 2.81 17.14" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "3" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_4_e96fc17d" uuid = "e96fc17d" id = "at_4_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "2.92 2.06 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "4" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_5_e96fc17d" uuid = "e96fc17d" id = "at_5_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "4.22 1.31 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "5" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_6_e96fc17d" uuid = "e96fc17d" id = "at_6_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "0.32 5.06 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "6" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_7_e96fc17d" uuid = "e96fc17d" id = "at_7_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "1.62 4.31 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "7" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_8_e96fc17d" uuid = "e96fc17d" id = "at_8_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "2.92 3.56 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "8" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_9_e96fc17d" uuid = "e96fc17d" id = "at_9_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "2.96 5.12 17.27" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "9" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_10_e96fc17d" uuid = "e96fc17d" id = "at_10_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "1.62 4.31 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "10" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_11_e96fc17d" uuid = "e96fc17d" id = "at_11_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "2.92 3.56 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "11" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_12_e96fc17d" uuid = "e96fc17d" id = "at_12_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-0.98 7.32 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "12" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_13_e96fc17d" uuid = "e96fc17d" id = "at_13_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "0.32 6.57 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "13" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_14_e96fc17d" uuid = "e96fc17d" id = "at_14_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "1.62 5.81 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "14" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_15_e96fc17d" uuid = "e96fc17d" id = "at_15_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "1.62 7.34 17.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "15" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_16_e96fc17d" uuid = "e96fc17d" id = "at_16_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "0.32 6.57 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "16" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_17_e96fc17d" uuid = "e96fc17d" id = "at_17_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "1.62 5.81 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "17" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_18_e96fc17d" uuid = "e96fc17d" id = "at_18_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "2.92 0.56 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "18" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_19_e96fc17d" uuid = "e96fc17d" id = "at_19_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-0.98 8.82 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "19" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_20_e96fc17d" uuid = "e96fc17d" id = "at_20_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "0.32 8.07 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "20" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_21_e96fc17d" uuid = "e96fc17d" id = "at_21_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "5.52 0.57 17.18" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "21" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_22_e96fc17d" uuid = "e96fc17d" id = "at_22_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-0.98 8.82 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "22" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_23_e96fc17d" uuid = "e96fc17d" id = "at_23_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "0.32 8.07 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "23" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_24_e96fc17d" uuid = "e96fc17d" id = "at_24_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "4.22 2.81 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "24" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_25_e96fc17d" uuid = "e96fc17d" id = "at_25_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "5.52 2.06 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "25" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_26_e96fc17d" uuid = "e96fc17d" id = "at_26_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "6.82 1.31 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "26" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_27_e96fc17d" uuid = "e96fc17d" id = "at_27_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "6.83 2.82 17.18" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "27" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_28_e96fc17d" uuid = "e96fc17d" id = "at_28_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "5.52 2.06 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "28" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_29_e96fc17d" uuid = "e96fc17d" id = "at_29_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "6.82 1.31 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "29" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_30_e96fc17d" uuid = "e96fc17d" id = "at_30_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "2.92 5.06 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "30" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_31_e96fc17d" uuid = "e96fc17d" id = "at_31_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "4.22 4.31 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "31" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_32_e96fc17d" uuid = "e96fc17d" id = "at_32_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "5.52 3.56 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "32" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_33_e96fc17d" uuid = "e96fc17d" id = "at_33_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "5.55 5.07 17.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "33" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_34_e96fc17d" uuid = "e96fc17d" id = "at_34_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "4.22 4.31 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "34" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_35_e96fc17d" uuid = "e96fc17d" id = "at_35_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "5.52 3.56 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "35" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_36_e96fc17d" uuid = "e96fc17d" id = "at_36_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "1.62 7.32 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "36" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_37_e96fc17d" uuid = "e96fc17d" id = "at_37_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "2.92 6.57 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "37" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_38_e96fc17d" uuid = "e96fc17d" id = "at_38_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "4.22 5.81 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "38" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_39_e96fc17d" uuid = "e96fc17d" id = "at_39_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "4.24 7.35 17.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "39" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_40_e96fc17d" uuid = "e96fc17d" id = "at_40_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "2.92 6.57 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "40" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_41_e96fc17d" uuid = "e96fc17d" id = "at_41_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "4.22 5.81 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "41" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_42_e96fc17d" uuid = "e96fc17d" id = "at_42_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "5.52 0.56 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "42" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_43_e96fc17d" uuid = "e96fc17d" id = "at_43_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "1.62 8.82 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "43" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_44_e96fc17d" uuid = "e96fc17d" id = "at_44_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "2.92 8.07 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "44" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_45_e96fc17d" uuid = "e96fc17d" id = "at_45_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "8.12 0.58 17.18" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "45" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_46_e96fc17d" uuid = "e96fc17d" id = "at_46_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "1.62 8.82 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "46" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_47_e96fc17d" uuid = "e96fc17d" id = "at_47_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "2.92 8.07 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "47" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_48_e96fc17d" uuid = "e96fc17d" id = "at_48_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "6.82 2.81 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "48" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_49_e96fc17d" uuid = "e96fc17d" id = "at_49_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "8.12 2.06 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "49" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_50_e96fc17d" uuid = "e96fc17d" id = "at_50_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "9.42 1.31 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "50" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_51_e96fc17d" uuid = "e96fc17d" id = "at_51_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-1.0 2.82 17.16" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "51" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_52_e96fc17d" uuid = "e96fc17d" id = "at_52_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "8.12 2.06 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "52" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_53_e96fc17d" uuid = "e96fc17d" id = "at_53_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "9.42 1.31 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "53" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_54_e96fc17d" uuid = "e96fc17d" id = "at_54_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "5.52 5.06 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "54" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_55_e96fc17d" uuid = "e96fc17d" id = "at_55_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "6.82 4.31 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "55" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_56_e96fc17d" uuid = "e96fc17d" id = "at_56_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "8.12 3.56 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "56" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_57_e96fc17d" uuid = "e96fc17d" id = "at_57_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-2.27 5.08 17.19" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "57" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_58_e96fc17d" uuid = "e96fc17d" id = "at_58_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "6.82 4.31 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "58" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_59_e96fc17d" uuid = "e96fc17d" id = "at_59_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "8.12 3.56 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "59" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_60_e96fc17d" uuid = "e96fc17d" id = "at_60_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "4.22 7.32 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "60" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_61_e96fc17d" uuid = "e96fc17d" id = "at_61_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "5.52 6.57 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "61" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_62_e96fc17d" uuid = "e96fc17d" id = "at_62_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "6.82 5.81 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "62" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_63_e96fc17d" uuid = "e96fc17d" id = "at_63_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-3.57 7.32 17.18" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "63" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_64_e96fc17d" uuid = "e96fc17d" id = "at_64_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "5.52 6.57 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "64" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_65_e96fc17d" uuid = "e96fc17d" id = "at_65_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "6.82 5.81 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "65" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_66_e96fc17d" uuid = "e96fc17d" id = "at_66_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "8.12 0.56 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "66" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_67_e96fc17d" uuid = "e96fc17d" id = "at_67_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "4.22 8.82 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "67" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_68_e96fc17d" uuid = "e96fc17d" id = "at_68_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "5.52 8.07 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "68" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_69_e96fc17d" uuid = "e96fc17d" id = "at_69_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "0.31 0.56 17.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "69" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_70_e96fc17d" uuid = "e96fc17d" id = "at_70_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "4.22 8.82 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "70" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_71_e96fc17d" uuid = "e96fc17d" id = "at_71_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "5.52 8.07 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "71" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_72_e96fc17d" uuid = "e96fc17d" id = "at_72_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-0.98 2.81 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "72" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_73_e96fc17d" uuid = "e96fc17d" id = "at_73_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "0.32 2.06 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "73" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_74_e96fc17d" uuid = "e96fc17d" id = "at_74_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "1.62 1.31 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "74" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_75_e96fc17d" uuid = "e96fc17d" id = "at_75_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "1.6 2.77 17.25" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "75" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_76_e96fc17d" uuid = "e96fc17d" id = "at_76_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "0.32 2.06 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "76" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_77_e96fc17d" uuid = "e96fc17d" id = "at_77_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "1.62 1.31 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "77" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_78_e96fc17d" uuid = "e96fc17d" id = "at_78_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-2.28 5.06 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "78" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_79_e96fc17d" uuid = "e96fc17d" id = "at_79_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-0.98 4.31 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "79" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_80_e96fc17d" uuid = "e96fc17d" id = "at_80_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "0.32 3.56 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "80" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_81_e96fc17d" uuid = "e96fc17d" id = "at_81_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "0.29 5.1 17.14" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "81" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_82_e96fc17d" uuid = "e96fc17d" id = "at_82_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-0.98 4.31 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "82" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_83_e96fc17d" uuid = "e96fc17d" id = "at_83_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "0.32 3.56 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "83" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_84_e96fc17d" uuid = "e96fc17d" id = "at_84_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-3.58 7.32 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "84" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_85_e96fc17d" uuid = "e96fc17d" id = "at_85_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-2.28 6.57 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "85" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_86_e96fc17d" uuid = "e96fc17d" id = "at_86_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-0.98 5.81 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "86" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_87_e96fc17d" uuid = "e96fc17d" id = "at_87_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-0.98 7.32 17.19" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "87" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_88_e96fc17d" uuid = "e96fc17d" id = "at_88_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-2.28 6.57 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "88" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_89_e96fc17d" uuid = "e96fc17d" id = "at_89_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-0.98 5.81 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "89" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_90_e96fc17d" uuid = "e96fc17d" id = "at_90_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "0.32 0.56 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "90" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_91_e96fc17d" uuid = "e96fc17d" id = "at_91_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-3.58 8.82 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "91" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_92_e96fc17d" uuid = "e96fc17d" id = "at_92_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-2.28 8.07 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "92" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_93_e96fc17d" uuid = "e96fc17d" id = "at_93_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "2.93 0.54 17.16" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "93" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_94_e96fc17d" uuid = "e96fc17d" id = "at_94_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-3.58 8.82 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "94" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_95_e96fc17d" uuid = "e96fc17d" id = "at_95_e96fc17d" radius = "1.32" name = "at_e96fc17d" translation = "-2.28 8.07 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "95" uuid = "e96fc17d" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_C" id = "as_C_e96fc17d" > 
             <Appearance DEF = "app_C" > 
               <Material name = "am_e96fc17d" diffuseColor = "0.565 0.565 0.565" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_C" radius = "0.76" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_98_e96fc17d" uuid = "e96fc17d" id = "at_98_e96fc17d" radius = "0.76" name = "at_e96fc17d" translation = "2.6 4.5 19.11" scale = "1 1 1" > 
           <Shape kind = "C" index = "98" uuid = "e96fc17d" > 
             <Appearance USE = "app_C" > 
             </Appearance> 
             <Sphere USE = "asp_C" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Group> 
       <Shape > 
         <IndexedLineSet coordIndex = "0 1 -1 0 2 -1 0 4 -1 1 3 -1 1 5 -1 2 3 -1 2 6 -1 3 7 -1 4 5 -1 4 6 -1 5 7 -1 6 7 -1" > 
           <Coordinate point = "[[ 0.00000000e+00  0.00000000e+00  0.00000000e+00  0.00000000e+00
   0.00000000e+00  3.82134074e+01 -5.20018609e+00  9.00698651e+00
   6.00000000e-16 -5.20018609e+00  9.00698651e+00  3.82134074e+01
   1.04003722e+01  0.00000000e+00  6.00000000e-16  1.04003722e+01
   0.00000000e+00  3.82134074e+01  5.20018609e+00  9.00698651e+00
   1.20000000e-15  5.20018609e+00  9.00698651e+00  3.82134074e+01]]" > 
           </Coordinate> 
         </IndexedLineSet> 
         <Appearance > 
           <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
           </Material> 
         </Appearance> 
       </Shape> 
       <Switch id = "switch_marker_0_e96fc17d" whichChoice = "-1" > 
         <Transform id = "marker_0_e96fc17d" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_1_e96fc17d" whichChoice = "-1" > 
         <Transform id = "marker_1_e96fc17d" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_2_e96fc17d" whichChoice = "-1" > 
         <Transform id = "marker_2_e96fc17d" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_3_e96fc17d" whichChoice = "-1" > 
         <Transform id = "marker_3_e96fc17d" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_4_e96fc17d" whichChoice = "-1" > 
         <Transform id = "marker_4_e96fc17d" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_line_0_e96fc17d" whichChoice = "-1" > 
         <Shape > 
           <IndexedLineSet id = "line_ind_0_e96fc17d" solid = "false" coordIndex = "0 1 -1" > 
             <Coordinate id = "line_coor_0_e96fc17d" point = "0 0 0 0 0 1" > 
             </Coordinate> 
           </IndexedLineSet> 
           <Appearance > 
             <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
             </Material> 
           </Appearance> 
         </Shape> 
       </Switch> 
       <Switch id = "switch_line_1_e96fc17d" whichChoice = "-1" > 
         <Shape > 
           <IndexedLineSet id = "line_ind_1_e96fc17d" solid = "false" coordIndex = "0 1 -1" > 
             <Coordinate id = "line_coor_1_e96fc17d" point = "0 0 0 0 0 1" > 
             </Coordinate> 
           </IndexedLineSet> 
           <Appearance > 
             <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
             </Material> 
           </Appearance> 
         </Shape> 
       </Switch> 
       <TimeSensor DEF = "time" cycleInterval = "10" loop = "true" > 
       </TimeSensor> 
       <PositionInterpolator DEF = "move_0" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_0" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_0" fromField = "value_changed" toNode = "at_0_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_1" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_1" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_1" fromField = "value_changed" toNode = "at_1_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_2" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_2" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_2" fromField = "value_changed" toNode = "at_2_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_3" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.259788985951393 2.808094618616392 17.14004881828876 4.253851148500147 2.806771204245024 17.144618773645796 4.244141285920468 2.809886385301188 17.152051741928997 4.2383264292224725 2.8123681648367453 17.159177206575418 4.232209374402933 2.808089960802247 17.162381236221385 4.228775900848495 2.8049581914336335 17.163289883025996 4.2251769667615005 2.800760880114074 17.16453905399382 4.22224614078086 2.796963936126011 17.16628424807547 4.219589278347441 2.7938257604923664 17.16887133315454 4.216748610674954 2.792826820083931 17.17311496560258 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_3" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_3" fromField = "value_changed" toNode = "at_3_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_4" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_4" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_4" fromField = "value_changed" toNode = "at_4_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_5" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_5" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_5" fromField = "value_changed" toNode = "at_5_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_6" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_6" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_6" fromField = "value_changed" toNode = "at_6_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_7" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_7" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_7" fromField = "value_changed" toNode = "at_7_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_8" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_8" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_8" fromField = "value_changed" toNode = "at_8_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_9" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.958787924211506 5.117329070203434 17.267716247229032 2.886161398203197 5.073396726449609 17.35782815766074 2.883217981810934 5.06051914443327 17.343696299478342 2.931468689329704 5.08489708997607 17.246213428532727 2.9504522177298567 5.089634492650436 17.257902010475945 2.9607114950942166 5.0932471110850095 17.274258927606592 2.9698309168141472 5.0978742429210895 17.290350007664976 2.969898556307013 5.098450697614498 17.287158084005544 2.9523614294683047 5.088745217049172 17.25791288437636 2.917306280284471 5.06807528772248 17.214906560858612 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_9" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_9" fromField = "value_changed" toNode = "at_9_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_10" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_10" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_10" fromField = "value_changed" toNode = "at_10_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_11" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_11" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_11" fromField = "value_changed" toNode = "at_11_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_12" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_12" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_12" fromField = "value_changed" toNode = "at_12_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_13" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_13" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_13" fromField = "value_changed" toNode = "at_13_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_14" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_14" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_14" fromField = "value_changed" toNode = "at_14_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_15" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6150952205736835 7.340481954620783 17.172382611431736 1.616740822508942 7.342601313056279 17.171175420279535 1.6174301113125655 7.337292373452567 17.17401144517547 1.6168749443990322 7.330329490684114 17.17689031934092 1.6139375016386714 7.330439496134304 17.17488891115462 1.6115693948885563 7.330615325324347 17.174061147117296 1.6075879276879148 7.331083213824082 17.17307424596081 1.6035569199759032 7.3315419633508245 17.17250281062996 1.5999314085144054 7.3317388145331845 17.1729366359852 1.5976446184073307 7.3315550506976095 17.17584802402066 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_15" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_15" fromField = "value_changed" toNode = "at_15_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_16" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_16" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_16" fromField = "value_changed" toNode = "at_16_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_17" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_17" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_17" fromField = "value_changed" toNode = "at_17_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_18" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_18" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_18" fromField = "value_changed" toNode = "at_18_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_19" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_19" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_19" fromField = "value_changed" toNode = "at_19_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_20" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_20" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_20" fromField = "value_changed" toNode = "at_20_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_21" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.524550094223409 0.5666000592715079 17.180721978523756 5.5239501122461 0.5650798383378719 17.181745041019777 5.523263915312645 0.564735633734558 17.1810257001484 5.523069324110642 0.564131691404467 17.179651092700073 5.5222033064473415 0.5636656739996412 17.179279357660256 5.521459613101439 0.5637391520429821 17.179755112824143 5.520410325423291 0.5639722746807468 17.18044857728895 5.519698396305601 0.5640538475616453 17.181168309086704 5.51933092201621 0.5641743059005456 17.181642394307502 5.517591816311055 0.5649065048648777 17.18075585749683 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_21" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_21" fromField = "value_changed" toNode = "at_21_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_22" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_22" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_22" fromField = "value_changed" toNode = "at_22_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_23" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_23" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_23" fromField = "value_changed" toNode = "at_23_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_24" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_24" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_24" fromField = "value_changed" toNode = "at_24_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_25" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_25" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_25" fromField = "value_changed" toNode = "at_25_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_26" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_26" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_26" fromField = "value_changed" toNode = "at_26_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_27" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.832683927120229 2.8187455577345264 17.177451357526557 6.827949088721082 2.8184156172632515 17.1810400416792 6.826548600570641 2.8178200466802266 17.180718555938025 6.827702188506759 2.816505170707478 17.17937385059578 6.8279987355824066 2.8149392495257235 17.176568629451655 6.827204924521319 2.8145752701245708 17.175815540324514 6.825570833824822 2.8145265125932513 17.175689898918044 6.823320681582202 2.815436322800115 17.175926256274227 6.820384974439251 2.8175543722288734 17.176181820194493 6.817304138323884 2.818844623697849 17.176814181465666 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_27" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_27" fromField = "value_changed" toNode = "at_27_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_28" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_28" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_28" fromField = "value_changed" toNode = "at_28_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_29" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_29" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_29" fromField = "value_changed" toNode = "at_29_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_30" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_30" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_30" fromField = "value_changed" toNode = "at_30_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_31" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_31" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_31" fromField = "value_changed" toNode = "at_31_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_32" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_32" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_32" fromField = "value_changed" toNode = "at_32_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_33" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.5516328350267585 5.072953908378388 17.1655574999412 5.537773333151553 5.07280539080483 17.167889069928723 5.532657732101196 5.072162503446396 17.171935368283787 5.5208783014474125 5.073524229383901 17.21351971139638 5.50955653886373 5.07811614167309 17.246774610497976 5.501350014690544 5.082329926500726 17.257826039477443 5.495022907272117 5.085461606290797 17.258330869780398 5.490873085783682 5.086330041393438 17.25480743894146 5.490765643949278 5.084459603864953 17.247518276473272 5.51140539428603 5.071502902619864 17.221567827560648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_33" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_33" fromField = "value_changed" toNode = "at_33_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_34" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_34" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_34" fromField = "value_changed" toNode = "at_34_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_35" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_35" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_35" fromField = "value_changed" toNode = "at_35_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_36" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_36" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_36" fromField = "value_changed" toNode = "at_36_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_37" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_37" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_37" fromField = "value_changed" toNode = "at_37_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_38" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_38" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_38" fromField = "value_changed" toNode = "at_38_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_39" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.23830080909136 7.353226514635221 17.165652884285706 4.231823005898266 7.338078197176951 17.169595877889616 4.228314727681517 7.331856776412569 17.173113125535856 4.227939549662522 7.327398815163299 17.188681828583835 4.226888659686216 7.320881584206554 17.207622766493056 4.2254754177015705 7.313181150808558 17.222527218514145 4.22337490364432 7.302709425450515 17.23771781524754 4.221034293509371 7.2930060609257685 17.247171407326412 4.218902808538599 7.288927807798966 17.24717280368249 4.217027631335194 7.311582597892179 17.223481350792028 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_39" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_39" fromField = "value_changed" toNode = "at_39_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_40" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_40" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_40" fromField = "value_changed" toNode = "at_40_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_41" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_41" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_41" fromField = "value_changed" toNode = "at_41_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_42" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_42" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_42" fromField = "value_changed" toNode = "at_42_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_43" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_43" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_43" fromField = "value_changed" toNode = "at_43_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_44" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_44" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_44" fromField = "value_changed" toNode = "at_44_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_45" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "8.118949606548945 0.5761077974052543 17.176214748268954 8.118437543477649 0.5706439240062182 17.179437301897163 8.118459539327189 0.5697356415902016 17.179254588674244 8.118323676448266 0.5695992084894744 17.178393552511977 8.118443035685663 0.5697333176452197 17.17766203780136 8.118471710743572 0.5697025480254416 17.177317604811908 8.118446138965709 0.5693499902217247 17.176971072656507 8.118763875264825 0.5678332556427169 17.17684339611858 8.119770099809093 0.5647666438550235 17.176793923436097 8.118957673151554 0.5634987438915893 17.177246574327764 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_45" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_45" fromField = "value_changed" toNode = "at_45_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_46" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_46" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_46" fromField = "value_changed" toNode = "at_46_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_47" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_47" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_47" fromField = "value_changed" toNode = "at_47_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_48" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_48" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_48" fromField = "value_changed" toNode = "at_48_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_49" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_49" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_49" fromField = "value_changed" toNode = "at_49_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_50" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_50" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_50" fromField = "value_changed" toNode = "at_50_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_51" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.003670097976923 2.8153878983697185 17.164349133677955 -0.995006946189908 2.815114717546221 17.16787546588587 -0.9920574570160134 2.8150667524295265 17.17086160950587 -0.9882047077719481 2.8167837092634125 17.17437928509771 -0.9861384720386936 2.816946014459344 17.17618588206909 -0.9852193371034811 2.8168970325707967 17.177340857652663 -0.9842628956710465 2.816761514633459 17.178839699781356 -0.9835445444107129 2.816497925723929 17.180222930186066 -0.9832998203380469 2.8161821972556624 17.18116545891326 -0.9833842714448553 2.8166030726096105 17.180735094634105 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_51" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_51" fromField = "value_changed" toNode = "at_51_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_52" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_52" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_52" fromField = "value_changed" toNode = "at_52_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_53" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_53" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_53" fromField = "value_changed" toNode = "at_53_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_54" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_54" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_54" fromField = "value_changed" toNode = "at_54_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_55" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_55" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_55" fromField = "value_changed" toNode = "at_55_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_56" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_56" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_56" fromField = "value_changed" toNode = "at_56_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_57" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.273969963203836 5.075493368840874 17.18801608731615 -2.276543524543992 5.072413276442889 17.184423678300693 -2.27668361827354 5.071569657823249 17.18297415884659 -2.2750312789613716 5.070947975578588 17.181611933520184 -2.2746088226029317 5.069676049195909 17.179400377302365 -2.2748319263805428 5.068840560435677 17.178840411797918 -2.2760376638188906 5.068021016810621 17.178440465148338 -2.2786856088659495 5.067386291765055 17.17801127905592 -2.2828523459020187 5.066732927092535 17.177426017343137 -2.284700632427861 5.066357642720443 17.17694104651217 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_57" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_57" fromField = "value_changed" toNode = "at_57_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_58" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_58" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_58" fromField = "value_changed" toNode = "at_58_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_59" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_59" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_59" fromField = "value_changed" toNode = "at_59_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_60" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_60" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_60" fromField = "value_changed" toNode = "at_60_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_61" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_61" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_61" fromField = "value_changed" toNode = "at_61_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_62" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_62" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_62" fromField = "value_changed" toNode = "at_62_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_63" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.570258420626211 7.323411819534487 17.177389981461047 -3.573545852332467 7.32079687051344 17.179114442357864 -3.574149462877879 7.320943120960068 17.17943999917004 -3.5731705834885275 7.322428420434946 17.179389818039706 -3.5707924453556754 7.324215154554329 17.17718103850565 -3.5685484519456447 7.325921048791149 17.175297056971733 -3.5657963698565314 7.3279915527781645 17.17293895739286 -3.5640838205594005 7.329448186671053 17.17168558333548 -3.562959439482369 7.330410421601363 17.171796626715228 -3.5624520483245004 7.331783319524079 17.172921847842925 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_63" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_63" fromField = "value_changed" toNode = "at_63_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_64" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_64" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_64" fromField = "value_changed" toNode = "at_64_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_65" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_65" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_65" fromField = "value_changed" toNode = "at_65_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_66" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_66" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_66" fromField = "value_changed" toNode = "at_66_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_67" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_67" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_67" fromField = "value_changed" toNode = "at_67_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_68" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_68" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_68" fromField = "value_changed" toNode = "at_68_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_69" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3118842478912281 0.5580828427466905 17.165508073583833 0.3134582921933333 0.5577393556769751 17.171121461710424 0.3151038671747961 0.5606443152226912 17.173466845287027 0.3159035902030446 0.5629537048440494 17.17323802120856 0.31659654200824394 0.5648114188652197 17.173534853558678 0.316996883031893 0.5657537277560075 17.174133374989143 0.31715243299371815 0.5662204587536063 17.175031174205653 0.31643283076451323 0.5651409994376163 17.17575553317418 0.31484837030499974 0.5626613408548661 17.176345968398778 0.31348227841018333 0.5634905760253299 17.176682299116194 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_69" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_69" fromField = "value_changed" toNode = "at_69_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_70" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_70" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_70" fromField = "value_changed" toNode = "at_70_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_71" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_71" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_71" fromField = "value_changed" toNode = "at_71_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_72" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_72" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_72" fromField = "value_changed" toNode = "at_72_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_73" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_73" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_73" fromField = "value_changed" toNode = "at_73_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_74" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_74" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_74" fromField = "value_changed" toNode = "at_74_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_75" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.598432103651343 2.771617841564511 17.24565190477618 1.6068356843890972 2.775070274884183 17.223141860820473 1.6078683216812795 2.7936915984960202 17.198704180062002 1.6066029564689852 2.802923306553489 17.180632719803206 1.6085424986615868 2.806563753577398 17.182363999253273 1.6096982436957317 2.8085449458387575 17.18156873098804 1.611535563217417 2.8112346856718458 17.18084595302766 1.6133076466185656 2.8137760150870146 17.179714837414036 1.61484901568983 2.816310921855369 17.178238609327316 1.6157528957134897 2.8187964509426804 17.17687608622652 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_75" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_75" fromField = "value_changed" toNode = "at_75_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_76" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_76" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_76" fromField = "value_changed" toNode = "at_76_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_77" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_77" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_77" fromField = "value_changed" toNode = "at_77_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_78" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_78" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_78" fromField = "value_changed" toNode = "at_78_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_79" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_79" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_79" fromField = "value_changed" toNode = "at_79_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_80" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_80" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_80" fromField = "value_changed" toNode = "at_80_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_81" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.29468933916308093 5.096778716129049 17.142783955833536 0.31033603369206314 5.085139760743297 17.166132425371735 0.32139151552128914 5.075893186770424 17.185147924476336 0.3215897774953296 5.0734358579444825 17.174077868637855 0.31881625442263367 5.071353584146399 17.168539872426898 0.318099160832236 5.070039605241535 17.168335365356583 0.31809305257892717 5.06854538804377 17.17012531041909 0.3186717449092909 5.067382804467825 17.1721026147021 0.3196987433619107 5.066372394248179 17.174327998164376 0.3200493953827482 5.066245765940367 17.1786507797658 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_81" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_81" fromField = "value_changed" toNode = "at_81_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_82" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_82" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_82" fromField = "value_changed" toNode = "at_82_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_83" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_83" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_83" fromField = "value_changed" toNode = "at_83_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_84" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_84" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_84" fromField = "value_changed" toNode = "at_84_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_85" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_85" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_85" fromField = "value_changed" toNode = "at_85_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_86" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_86" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_86" fromField = "value_changed" toNode = "at_86_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_87" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9771239534695154 7.321020153512626 17.188761949851393 -0.9802968722709857 7.323710291626151 17.184379103829656 -0.9802903551772854 7.322743031746944 17.18278347147008 -0.9792375728416582 7.321733720162692 17.183156099044975 -0.9793727916309366 7.321405648420586 17.18269389329149 -0.979313476093004 7.321227349716283 17.182705002846284 -0.9798477350506484 7.320747499375684 17.18269015651587 -0.980517061220212 7.319907542921909 17.18275353163988 -0.9812728983614608 7.318645301458508 17.18280951935849 -0.9819819366060177 7.317693085293363 17.181262158528433 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_87" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_87" fromField = "value_changed" toNode = "at_87_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_88" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_88" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_88" fromField = "value_changed" toNode = "at_88_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_89" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_89" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_89" fromField = "value_changed" toNode = "at_89_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_90" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_90" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_90" fromField = "value_changed" toNode = "at_90_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_91" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_91" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_91" fromField = "value_changed" toNode = "at_91_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_92" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_92" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_92" fromField = "value_changed" toNode = "at_92_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_93" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9298898575601426 0.5436100471350247 17.158874348813438 2.9293561842226 0.548213679806127 17.163289462181037 2.9258770692577714 0.5530362791894217 17.1666978576002 2.9245620444737934 0.5543694189172504 17.16740123169713 2.922995826993285 0.556564285820975 17.17026098538145 2.9219096255305717 0.558389644029119 17.172676747847714 2.9205810877329244 0.5606408835768966 17.17570210004265 2.919076255583382 0.5623113346273487 17.17828701480223 2.917292037118944 0.5635657364007127 17.18021777101639 2.914788356205876 0.5649149459211363 17.181125000140707 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_93" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_93" fromField = "value_changed" toNode = "at_93_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_94" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_94" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_94" fromField = "value_changed" toNode = "at_94_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_95" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_95" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_95" fromField = "value_changed" toNode = "at_95_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_96" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.0361483069584887 5.20007405002636 20.19279303332487 2.8059763912127584 5.377568711459945 20.268502489540005 2.7916602591182342 5.355161917686986 20.491326740057197 2.4650753559161194 5.437280190566882 20.533168907866717 2.367823814257942 5.405442705055912 20.81000258268763 2.4166164243559325 5.408124742874461 20.980257636203273 2.500563305395832 5.423747281185289 21.180081474513027 2.5603963392005014 5.441429478673612 21.344964555816738 2.5993832301911994 5.448381044460674 21.490062693435057 2.6345380022348195 5.43384028729841 21.642802174291386 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_96" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_96" fromField = "value_changed" toNode = "at_96_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_97" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.9502278733964309 3.436180216297302 19.305458682638857 2.2156033609985246 3.2920040360211225 19.57653770442705 2.370478694986324 3.2076405828285304 19.866211427108066 2.4343298178116783 3.1886934375887837 19.869116203933427 2.6391927341631765 3.2194344988411046 20.08221668496463 2.8148497528431795 3.211948195643591 20.265904616033694 3.082224027301458 3.2331756676929206 20.529597075087302 3.3576988049330474 3.296809155048445 20.795557354335212 3.6334162170488913 3.37201813404391 21.056743445447758 3.9319718470150327 3.477949508369305 21.35395285210702 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_97" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_97" fromField = "value_changed" toNode = "at_97_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_98" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.600093043105944 4.503493255065873 19.106703718109742 2.429479613586714 4.476562934374556 19.277313249143294 2.4041981956172718 4.390111610877912 19.557331071616225 2.433848550207283 4.325216012808602 20.119096603269455 2.5865257569056785 4.32564845169605 20.44555796554325 2.6750982326031134 4.324910452999787 20.635933933956657 2.8239379692461783 4.342996152890431 20.871446729056796 2.980550750511061 4.363927614081901 21.078942924047023 3.127808136605606 4.405630737933902 21.277715752425816 3.283436387670884 4.4557743064382835 21.49976359872498 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_98" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_98" fromField = "value_changed" toNode = "at_98_e96fc17d" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_99" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.73736823277318 4.712565147695313 21.000030423760517 3.4494871136894427 4.9389534322585 20.84458484686238 3.7696912002686456 5.288707574494619 20.46100304349328 4.125018236545045 5.316987504150797 20.083056172383596 4.244697389699349 5.444957648265977 19.534378333109224 4.240661223236614 5.554250236656448 19.22034633966748 4.186465845496893 5.652058865814936 18.873407653877653 4.151538299723717 5.711448243127047 18.5947193391631 4.165774399706973 5.760721142461393 18.366744613278012 4.201603867873281 5.804965402064039 18.141274260641893 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_99" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_99" fromField = "value_changed" toNode = "at_99_e96fc17d" toField = "translation" > 
       </ROUTE> 
     </Scene> 
   </X3D> 
 </div> 

<script type="text/javascript" src="https://code.jquery.com/jquery-2.1.0.min.js" ></script>
 <script >
 if (atoms_dict == undefined) {var atoms_dict = {"new": true};}; 
atoms_dict["e96fc17d"] = {com: "2.70 4.69 11.95", 
top_pos: "2.70 4.69 50.95", 
front_pos: "2.70 -34.31 11.95", 
right_pos: "41.70 4.69 11.95", 
left_pos: "-36.30 4.69 11.95", 
top_ori: "0 0 0 0", 
front_ori: "1 0 0 1.57079", 
right_ori: "0 1 0 1.57079", 
left_ori: "0 1 0 -1.57079", 
select: [], 
uuid: "e96fc17d", 
label: "False", 
bond: "False", 
polyhedra: {}, 
};

// if (atoms_dict[uuid]['new']){
function setQuality(uuid, quality){
        // $('#error_'.concat(atoms_dict[uuid]['uuid'])).html('uuid: '.concat(atoms_dict[uuid]["uuid"]));
        var x3d = 'x3dase_' + uuid;
        document.getElementById(x3d).setAttribute('PrimitiveQuality', quality);
    }
function set_viewpoint(uuid, pos, ori){
    // $('#error_'.concat(atoms_dict[uuid]['uuid'])).html('uuid: '.concat(atoms_dict[uuid]["uuid"]));
    var persp = 'camera_persp_' + uuid;
    var ortho = 'camera_ortho_' + uuid;
    document.getElementById(persp).setAttribute('orientation', atoms_dict[uuid][ori]);
    document.getElementById(persp).setAttribute('position', atoms_dict[uuid][pos]);
    document.getElementById(ortho).setAttribute('orientation', atoms_dict[uuid][ori]);
    document.getElementById(ortho).setAttribute('position', atoms_dict[uuid][pos]);
}

//Round a float value to x.xx format
function roundWithTwoDecimals(value)
{
    var x = (Math.round(value * 100)) / 100;
    var y = x.toFixed(2);
    return y;
}
//Handle click on any group member
function handleGroupClick(event)
{
    //Mark hitting point
    var target = event.target;
    var uuid = target.parentNode.getAttribute('uuid')
    var radius = target.parentNode.getAttribute('radius');
    var scale = target.parentNode.getAttribute('scale');
    scale = parseFloat(radius)*parseFloat(scale)*1.2;
    var scale = ' ' + scale + ' ' + scale + ' ' + scale;
    var translation = target.parentNode.getAttribute('translation');
    var id = target.parentNode.getAttribute('id');
    if (window.event.ctrlKey) {
        atoms_dict[uuid]['select'].push(id);

    }
    else {
        for (var i=1; i<= atoms_dict[uuid]['select'].length; i++) {
            $('#switch_marker_' + i + '_' + uuid).attr('whichChoice', -1);
        }
        atoms_dict[uuid]['select'] = [];
        atoms_dict[uuid]['select'].push(id);
        $('#switch_marker_' + 2 + '_' + uuid).attr('whichChoice', -1);
}
    var n = atoms_dict[uuid]['select'].length;
    $('#switch_marker_' + n + '_' + uuid).attr('whichChoice', 0);
    $('#marker_' + n + '_' + uuid).attr('translation', translation);
    $('#marker_' + n + '_' + uuid).attr('scale', scale);
    var atom_kind = '#lastonMouseoverObject_kind_'.concat(uuid);
    var atom_index = '#lastonMouseoverObject_index_'.concat(uuid);
    $(atom_kind).html(target.getAttribute("kind"));
    $(atom_index).html(target.getAttribute("index"));
    //
    var coord = translation.split(" ")
    var atom_position = '#position_'.concat(uuid);
    var x = roundWithTwoDecimals(coord[0]);
    var y = roundWithTwoDecimals(coord[1]);
    var z = roundWithTwoDecimals(coord[2]);
    var position = 'x = ' + x + ' y = ' + y + ' z = ' + z;
    $(atom_position).html(position);

    if (atoms_dict[uuid]['select'].length == 2){
        calculate_distance(uuid);
        draw_line(uuid);
    }
    else if (atoms_dict[uuid]['select'].length == 3){
        calculate_angle(uuid);
        draw_line(uuid);
    }

    console.log(event);
}
//Add a onMouseover callback to every shape
$(document).ready(function(){
    $("shape").each(function() {
        // $(this).attr("onMouseover", "handleOnMouseover_shape(this)");
        // $(this).attr("onclick", "handleClick_shape(this)");
    });
    //Add a onMouseover callback to every transform
    $("transform").each(function() {
        // $(this).attr("onMouseover", "handleOnMouseover_transform(this)");
        // $(this).attr("onclick", "handleClick_transform(this)");
    });
});
$(document).on("click", function(e) {
    if (e.target === document || e.target.tagName === "BODY" || e.target.tagName === "HTML") {
        $('#marker').attr('scale', "0.0001 0.0001 0.0001");
    }
});
//Handle onMouseover on a shape
function handleOnMouseover_shape(shape)
{
    var atom_kind = '#lastonMouseoverObject_kind_'.concat($(shape).attr("uuid"));
    var atom_index = '#lastonMouseoverObject_index_'.concat($(shape).attr("uuid"));
    $(atom_kind).html($(shape).attr("kind"));
    $(atom_index).html($(shape).attr("index"));
}
//Handle onMouseover on a shape
function handleClick_shape(shape)
{
    var atom_kind = '#lastonMouseoverObject_kind_'.concat($(shape).attr("uuid"));
    var atom_index = '#lastonMouseoverObject_index_'.concat($(shape).attr("uuid"));
    $(atom_kind).html($(shape).attr("kind"));
    $(atom_index).html($(shape).attr("index"));
}
//Handle onMouseover on a transform
function handleOnMouseover_transform(transform)
{
    var atom_position = '#position_'.concat($(transform).attr("uuid"));
    var coord = $(transform).attr("translation").split(" "[0]);
    var x = roundWithTwoDecimals(coord[0]);
    var y = roundWithTwoDecimals(coord[1]);
    var z = roundWithTwoDecimals(coord[2]);
    var position = 'x = ' + x + ' y = ' + y + ' z = ' + z;
    $(atom_position).html(position);
}

function calculate_distance(uuid)
{
    var measure = '#measure_'.concat(uuid);
    var c1 = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation").split(" ");
    var c2 = document.getElementById(atoms_dict[uuid]['select'][1]).getAttribute("translation").split(" ");
    r = (c1[0] - c2[0])*(c1[0] - c2[0]) + (c1[1] - c2[1])*(c1[1] - c2[1]) + (c1[2] - c2[2])*(c1[2] - c2[2]);
    r = roundWithTwoDecimals(Math.sqrt(r));
    var dist = 'Distance:  ' + r;
    $(measure).html(dist);
}
function calculate_angle(uuid)
{
    var measure = '#measure_'.concat(uuid);
    var c1 = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation").split(" ");
    var c2 = document.getElementById(atoms_dict[uuid]['select'][1]).getAttribute("translation").split(" ");
    var c3 = document.getElementById(atoms_dict[uuid]['select'][2]).getAttribute("translation").split(" ");
    var AB = Math.sqrt(Math.pow(c2[0]-c1[0],2)+ Math.pow(c2[1]-c1[1],2) + Math.pow(c2[2]-c1[2],2));    
    var BC = Math.sqrt(Math.pow(c2[0]-c3[0],2)+ Math.pow(c2[1]-c3[1],2) + Math.pow(c2[2]-c3[2],2)); 
    var AC = Math.sqrt(Math.pow(c3[0]-c1[0],2)+ Math.pow(c3[1]-c1[1],2)+ Math.pow(c3[2]-c1[2],2));
    var angle = roundWithTwoDecimals(Math.acos((BC*BC+AB*AB-AC*AC)/(2*BC*AB))*180/3.1415926);
    var angle = 'angle:  ' + angle;
    $(measure).html(angle);
}
function draw_line(uuid)
{
    var n = atoms_dict[uuid]['select'].length;
    var coordIndex = '';
    var point = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation");
    for (var i = 1; i < n; i++) {
        var c1 = document.getElementById(atoms_dict[uuid]['select'][i]).getAttribute("translation");
        coordIndex = coordIndex + (i-1) + ' ' + i + ' -1 ';
        point = point + ' ' + c1 + ' ';
    }
    $('#line_coor_' + 0 + '_' + uuid).attr('point', point);
    $('#line_ind_' + 0 + '_' + uuid).attr('coordIndex', coordIndex);
    $('#switch_line_' + 0 + '_' + uuid).attr('whichChoice', 0);
}
//Handle models
function spacefilling(uuid)
{
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "1.0, 1.0, 1.0");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function ballstick(uuid)
{
    if (atoms_dict[uuid]['bond']=='False'){ 
        alert('Please set bond parameter in your code, e.g. bond=1.0!');
        $('#error_'.concat(uuid)).html('(^_^) Please set bond parameter in your code, e.g. bond=1.0!');
		return ;
    }
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "0.6, 0.6, 0.6");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function polyhedra(uuid)
{
    if (atoms_dict[uuid]['polyhedra'].length==0){ 
        alert('Please set polyhedra parameter in your code, e.g. polyhedra={"Ti": ["O"]}!');
        $('#error_'.concat(uuid)).html('(^_^) Please set polyhedra parameter in your code, e.g. polyhedra={"Ti": ["O"]}!');
		return ;
    }
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "0.6, 0.6, 0.6");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '0');
}
function none(uuid)
{
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.0");
        }
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
        
function element(uuid)
{
    if (atoms_dict[uuid]['label']=='False'){ 
        alert('To show element, please set label=True in your code!');
        $('#error_'.concat(uuid)).html('(^_^) To show element, please set label=True in your code!');
		return ;
	}
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.4");
        }
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function index(uuid)
{
    if (atoms_dict[uuid]['label']=='False'){ 
        alert('To show index, please set label=True in your code!');
        $('#error_'.concat(uuid)).html('(^_^) To show index, please set label=True in your code!');
		return ;
	}
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.4");
        }
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
// } 
</script> </body>
</html>
