<html>
<head>
<title>ASE atomic visualization</title>
<link rel="stylesheet" type="text/css"
 href="https://www.x3dom.org/x3dom/release/x3dom.css">
</link>
<script type="text/javascript"
 src="https://www.x3dom.org/x3dom/release/x3dom.js">
</script>
<style>
* {
    box-sizing: border-box;
  }
 
/* Create two unequal columns that floats next to each other */
.column {
  float: left;
  padding: 1px;
}

.left {
  width: 20%;
}

.right {
  width: 80%;
}

/* Clear floats after the columns */
.row:after {
  content: "";
  display: table;
  clear: both;
}

#x3dase{
    top:0;
    width: 80%;
    height: 80%;
    border:2px solid darkorange;        
}

#sidebar{
    top:0;
    border:2px solid darkorange;        
}


/* Sidebar component containers */
.ui-widget-header
{
  background-color: lightblue;
  font-size: 12px;

}
.sidebarComponent
{
    padding:2px 2px 2px 2px;
    font-size: medium;
}

.button {
  background-color: #4CAF50; /* Green */
  border: 1px solid green;
  color: white;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 10px;
  cursor: pointer;
  /* float: left; */
}

</style></head>
<body>
<div class = "column left", id = "sidebar">

    <div class="ui-widget-header">Model</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="spacefilling('037e348c')">Ball</button>
            <button type="button" class = "button" onclick="ballstick('037e348c')">Ball-and-stick</button>
            <button type="button" class = "button" onclick="polyhedra('037e348c')">Polyhedra</button>
        </form>
    </div>
    <div class="ui-widget-header">Label</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="none('037e348c')">None</button>
            <button type="button" class = "button" onclick="element('037e348c')"> Element</button>
            <button type="button" class = "button" onclick="index('037e348c')">Index</button>
        </form>
    </div>

    <div class="ui-widget-header">Camera</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="document.getElementById('camera_ortho_037e348c').setAttribute('set_bind','true');">Orthographic</button>
            <button type="button" class = "button" onclick="document.getElementById('camera_persp_037e348c').setAttribute('set_bind','true');">Perspective</button>
        </form>
    </div>

    <div class="ui-widget-header">View</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="set_viewpoint('037e348c', 'top_pos', 'top_ori')">Top</button>
            <button type="button" class = "button" onclick="set_viewpoint('037e348c', 'front_pos', 'front_ori')">Front</button>
            <button type="button" class = "button" onclick="set_viewpoint('037e348c', 'right_pos', 'right_ori')">Right</button>
        </form>
    </div>

    <div class="ui-widget-header">Measurement</div>
    <div class="sidebarComponent">
        <form style="text-align:left; font-size: 12px;">
            <table style="font-size:1.0em;">
                <td id="lastonMouseoverObject_kind_037e348c">-</td> <td id="lastonMouseoverObject_index_037e348c">-</td> 
                <td id="position_037e348c">-</td></tr>
            </table>
            <p id="measure_037e348c"></p>
            <p id="error_037e348c"></p>
        </form>
    </div>

</div>

<script>
    document.onkeyup = function(e) {
      var x = event.which || event.keyCode;
      var label = 0;
        if (x == 49) {
            set_viewpoint('037e348c', 'top_pos', 'top_ori');
        } else if (x == 50) {
            set_viewpoint('037e348c', 'front_pos', 'front_ori');
        } else if (x == 51) {
            set_viewpoint('037e348c', 'right_pos', 'right_ori');
        } else if (x == 83) {
          spacefilling('037e348c');
        } else if (x == 66) {
          ballstick('037e348c');
        } else if (x == 80) {
          polyhedra('037e348c');
        } else if (x == 52) {
            element('037e348c');
        } else if (x == 53) {
            index('037e348c');
        } else if (x == 54) {
            none('037e348c');
        }
      };
    </script>
 <div class = "column right" > 
   <X3D id = "x3dase" PrimitiveQuality = "high" > 
     <Scene > 
       <Transform id = "t_camera_persp_037e348c" rotation = "0 0 0 0" > 
         <Viewpoint id = "camera_persp_037e348c" position = "2.7031119638018093 4.688284535792894 50.946590819762534" centerOfRotation = "2.7031119638018093 4.688284535792894 11.945195173173312" orientation = "0 0 0 0" description = "camera" > 
         </Viewpoint> 
       </Transform> 
       <Transform id = "t_camera_ortho_037e348c" rotation = "0 0 0 0" > 
         <OrthoViewpoint id = "camera_ortho_037e348c" position = "2.7031119638018093 4.688284535792894 50.946590819762534" centerOfRotation = "2.7031119638018093 4.688284535792894 11.945195173173312" orientation = "0 0 0 0" fieldOfView = "-13.000465215529742 -13.000465215529742 13.000465215529742 13.000465215529742" description = "camera" > 
         </OrthoViewpoint> 
       </Transform> 
       <Group onclick = "handleGroupClick(event, '037e348c')" > 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_H" id = "as_H_037e348c" > 
             <Appearance DEF = "app_H" > 
               <Material name = "am_037e348c" diffuseColor = "1.0 1.0 1.0" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_H" radius = "0.31" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_99_037e348c" uuid = "037e348c" id = "at_99_037e348c" radius = "0.31" name = "at_037e348c" translation = "2.74 4.71 21.0" scale = "1 1 1" > 
           <Shape kind = "H" index = "99" uuid = "037e348c" > 
             <Appearance USE = "app_H" > 
             </Appearance> 
             <Sphere USE = "asp_H" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_O" id = "as_O_037e348c" > 
             <Appearance DEF = "app_O" > 
               <Material name = "am_037e348c" diffuseColor = "1.0 0.051 0.051" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_O" radius = "0.66" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_96_037e348c" uuid = "037e348c" id = "at_96_037e348c" radius = "0.66" name = "at_037e348c" translation = "3.04 5.2 20.19" scale = "1 1 1" > 
           <Shape kind = "O" index = "96" uuid = "037e348c" > 
             <Appearance USE = "app_O" > 
             </Appearance> 
             <Sphere USE = "asp_O" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_97_037e348c" uuid = "037e348c" id = "at_97_037e348c" radius = "0.66" name = "at_037e348c" translation = "1.95 3.44 19.31" scale = "1 1 1" > 
           <Shape kind = "O" index = "97" uuid = "037e348c" > 
             <Appearance USE = "app_O" > 
             </Appearance> 
             <Sphere USE = "asp_O" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_Cu" id = "as_Cu_037e348c" > 
             <Appearance DEF = "app_Cu" > 
               <Material name = "am_037e348c" diffuseColor = "0.784 0.502 0.2" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_Cu" radius = "1.32" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_0_037e348c" uuid = "037e348c" id = "at_0_037e348c" radius = "1.32" name = "at_037e348c" translation = "1.62 2.81 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "0" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_1_037e348c" uuid = "037e348c" id = "at_1_037e348c" radius = "1.32" name = "at_037e348c" translation = "2.92 2.06 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "1" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_2_037e348c" uuid = "037e348c" id = "at_2_037e348c" radius = "1.32" name = "at_037e348c" translation = "4.22 1.31 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "2" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_3_037e348c" uuid = "037e348c" id = "at_3_037e348c" radius = "1.32" name = "at_037e348c" translation = "4.26 2.81 17.14" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "3" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_4_037e348c" uuid = "037e348c" id = "at_4_037e348c" radius = "1.32" name = "at_037e348c" translation = "2.92 2.06 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "4" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_5_037e348c" uuid = "037e348c" id = "at_5_037e348c" radius = "1.32" name = "at_037e348c" translation = "4.22 1.31 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "5" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_6_037e348c" uuid = "037e348c" id = "at_6_037e348c" radius = "1.32" name = "at_037e348c" translation = "0.32 5.06 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "6" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_7_037e348c" uuid = "037e348c" id = "at_7_037e348c" radius = "1.32" name = "at_037e348c" translation = "1.62 4.31 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "7" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_8_037e348c" uuid = "037e348c" id = "at_8_037e348c" radius = "1.32" name = "at_037e348c" translation = "2.92 3.56 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "8" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_9_037e348c" uuid = "037e348c" id = "at_9_037e348c" radius = "1.32" name = "at_037e348c" translation = "2.96 5.12 17.27" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "9" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_10_037e348c" uuid = "037e348c" id = "at_10_037e348c" radius = "1.32" name = "at_037e348c" translation = "1.62 4.31 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "10" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_11_037e348c" uuid = "037e348c" id = "at_11_037e348c" radius = "1.32" name = "at_037e348c" translation = "2.92 3.56 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "11" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_12_037e348c" uuid = "037e348c" id = "at_12_037e348c" radius = "1.32" name = "at_037e348c" translation = "-0.98 7.32 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "12" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_13_037e348c" uuid = "037e348c" id = "at_13_037e348c" radius = "1.32" name = "at_037e348c" translation = "0.32 6.57 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "13" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_14_037e348c" uuid = "037e348c" id = "at_14_037e348c" radius = "1.32" name = "at_037e348c" translation = "1.62 5.81 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "14" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_15_037e348c" uuid = "037e348c" id = "at_15_037e348c" radius = "1.32" name = "at_037e348c" translation = "1.62 7.34 17.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "15" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_16_037e348c" uuid = "037e348c" id = "at_16_037e348c" radius = "1.32" name = "at_037e348c" translation = "0.32 6.57 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "16" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_17_037e348c" uuid = "037e348c" id = "at_17_037e348c" radius = "1.32" name = "at_037e348c" translation = "1.62 5.81 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "17" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_18_037e348c" uuid = "037e348c" id = "at_18_037e348c" radius = "1.32" name = "at_037e348c" translation = "2.92 0.56 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "18" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_19_037e348c" uuid = "037e348c" id = "at_19_037e348c" radius = "1.32" name = "at_037e348c" translation = "-0.98 8.82 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "19" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_20_037e348c" uuid = "037e348c" id = "at_20_037e348c" radius = "1.32" name = "at_037e348c" translation = "0.32 8.07 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "20" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_21_037e348c" uuid = "037e348c" id = "at_21_037e348c" radius = "1.32" name = "at_037e348c" translation = "5.52 0.57 17.18" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "21" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_22_037e348c" uuid = "037e348c" id = "at_22_037e348c" radius = "1.32" name = "at_037e348c" translation = "-0.98 8.82 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "22" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_23_037e348c" uuid = "037e348c" id = "at_23_037e348c" radius = "1.32" name = "at_037e348c" translation = "0.32 8.07 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "23" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_24_037e348c" uuid = "037e348c" id = "at_24_037e348c" radius = "1.32" name = "at_037e348c" translation = "4.22 2.81 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "24" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_25_037e348c" uuid = "037e348c" id = "at_25_037e348c" radius = "1.32" name = "at_037e348c" translation = "5.52 2.06 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "25" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_26_037e348c" uuid = "037e348c" id = "at_26_037e348c" radius = "1.32" name = "at_037e348c" translation = "6.82 1.31 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "26" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_27_037e348c" uuid = "037e348c" id = "at_27_037e348c" radius = "1.32" name = "at_037e348c" translation = "6.83 2.82 17.18" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "27" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_28_037e348c" uuid = "037e348c" id = "at_28_037e348c" radius = "1.32" name = "at_037e348c" translation = "5.52 2.06 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "28" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_29_037e348c" uuid = "037e348c" id = "at_29_037e348c" radius = "1.32" name = "at_037e348c" translation = "6.82 1.31 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "29" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_30_037e348c" uuid = "037e348c" id = "at_30_037e348c" radius = "1.32" name = "at_037e348c" translation = "2.92 5.06 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "30" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_31_037e348c" uuid = "037e348c" id = "at_31_037e348c" radius = "1.32" name = "at_037e348c" translation = "4.22 4.31 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "31" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_32_037e348c" uuid = "037e348c" id = "at_32_037e348c" radius = "1.32" name = "at_037e348c" translation = "5.52 3.56 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "32" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_33_037e348c" uuid = "037e348c" id = "at_33_037e348c" radius = "1.32" name = "at_037e348c" translation = "5.55 5.07 17.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "33" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_34_037e348c" uuid = "037e348c" id = "at_34_037e348c" radius = "1.32" name = "at_037e348c" translation = "4.22 4.31 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "34" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_35_037e348c" uuid = "037e348c" id = "at_35_037e348c" radius = "1.32" name = "at_037e348c" translation = "5.52 3.56 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "35" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_36_037e348c" uuid = "037e348c" id = "at_36_037e348c" radius = "1.32" name = "at_037e348c" translation = "1.62 7.32 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "36" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_37_037e348c" uuid = "037e348c" id = "at_37_037e348c" radius = "1.32" name = "at_037e348c" translation = "2.92 6.57 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "37" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_38_037e348c" uuid = "037e348c" id = "at_38_037e348c" radius = "1.32" name = "at_037e348c" translation = "4.22 5.81 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "38" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_39_037e348c" uuid = "037e348c" id = "at_39_037e348c" radius = "1.32" name = "at_037e348c" translation = "4.24 7.35 17.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "39" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_40_037e348c" uuid = "037e348c" id = "at_40_037e348c" radius = "1.32" name = "at_037e348c" translation = "2.92 6.57 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "40" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_41_037e348c" uuid = "037e348c" id = "at_41_037e348c" radius = "1.32" name = "at_037e348c" translation = "4.22 5.81 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "41" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_42_037e348c" uuid = "037e348c" id = "at_42_037e348c" radius = "1.32" name = "at_037e348c" translation = "5.52 0.56 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "42" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_43_037e348c" uuid = "037e348c" id = "at_43_037e348c" radius = "1.32" name = "at_037e348c" translation = "1.62 8.82 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "43" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_44_037e348c" uuid = "037e348c" id = "at_44_037e348c" radius = "1.32" name = "at_037e348c" translation = "2.92 8.07 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "44" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_45_037e348c" uuid = "037e348c" id = "at_45_037e348c" radius = "1.32" name = "at_037e348c" translation = "8.12 0.58 17.18" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "45" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_46_037e348c" uuid = "037e348c" id = "at_46_037e348c" radius = "1.32" name = "at_037e348c" translation = "1.62 8.82 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "46" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_47_037e348c" uuid = "037e348c" id = "at_47_037e348c" radius = "1.32" name = "at_037e348c" translation = "2.92 8.07 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "47" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_48_037e348c" uuid = "037e348c" id = "at_48_037e348c" radius = "1.32" name = "at_037e348c" translation = "6.82 2.81 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "48" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_49_037e348c" uuid = "037e348c" id = "at_49_037e348c" radius = "1.32" name = "at_037e348c" translation = "8.12 2.06 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "49" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_50_037e348c" uuid = "037e348c" id = "at_50_037e348c" radius = "1.32" name = "at_037e348c" translation = "9.42 1.31 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "50" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_51_037e348c" uuid = "037e348c" id = "at_51_037e348c" radius = "1.32" name = "at_037e348c" translation = "-1.0 2.82 17.16" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "51" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_52_037e348c" uuid = "037e348c" id = "at_52_037e348c" radius = "1.32" name = "at_037e348c" translation = "8.12 2.06 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "52" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_53_037e348c" uuid = "037e348c" id = "at_53_037e348c" radius = "1.32" name = "at_037e348c" translation = "9.42 1.31 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "53" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_54_037e348c" uuid = "037e348c" id = "at_54_037e348c" radius = "1.32" name = "at_037e348c" translation = "5.52 5.06 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "54" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_55_037e348c" uuid = "037e348c" id = "at_55_037e348c" radius = "1.32" name = "at_037e348c" translation = "6.82 4.31 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "55" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_56_037e348c" uuid = "037e348c" id = "at_56_037e348c" radius = "1.32" name = "at_037e348c" translation = "8.12 3.56 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "56" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_57_037e348c" uuid = "037e348c" id = "at_57_037e348c" radius = "1.32" name = "at_037e348c" translation = "-2.27 5.08 17.19" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "57" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_58_037e348c" uuid = "037e348c" id = "at_58_037e348c" radius = "1.32" name = "at_037e348c" translation = "6.82 4.31 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "58" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_59_037e348c" uuid = "037e348c" id = "at_59_037e348c" radius = "1.32" name = "at_037e348c" translation = "8.12 3.56 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "59" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_60_037e348c" uuid = "037e348c" id = "at_60_037e348c" radius = "1.32" name = "at_037e348c" translation = "4.22 7.32 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "60" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_61_037e348c" uuid = "037e348c" id = "at_61_037e348c" radius = "1.32" name = "at_037e348c" translation = "5.52 6.57 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "61" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_62_037e348c" uuid = "037e348c" id = "at_62_037e348c" radius = "1.32" name = "at_037e348c" translation = "6.82 5.81 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "62" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_63_037e348c" uuid = "037e348c" id = "at_63_037e348c" radius = "1.32" name = "at_037e348c" translation = "-3.57 7.32 17.18" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "63" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_64_037e348c" uuid = "037e348c" id = "at_64_037e348c" radius = "1.32" name = "at_037e348c" translation = "5.52 6.57 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "64" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_65_037e348c" uuid = "037e348c" id = "at_65_037e348c" radius = "1.32" name = "at_037e348c" translation = "6.82 5.81 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "65" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_66_037e348c" uuid = "037e348c" id = "at_66_037e348c" radius = "1.32" name = "at_037e348c" translation = "8.12 0.56 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "66" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_67_037e348c" uuid = "037e348c" id = "at_67_037e348c" radius = "1.32" name = "at_037e348c" translation = "4.22 8.82 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "67" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_68_037e348c" uuid = "037e348c" id = "at_68_037e348c" radius = "1.32" name = "at_037e348c" translation = "5.52 8.07 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "68" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_69_037e348c" uuid = "037e348c" id = "at_69_037e348c" radius = "1.32" name = "at_037e348c" translation = "0.31 0.56 17.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "69" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_70_037e348c" uuid = "037e348c" id = "at_70_037e348c" radius = "1.32" name = "at_037e348c" translation = "4.22 8.82 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "70" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_71_037e348c" uuid = "037e348c" id = "at_71_037e348c" radius = "1.32" name = "at_037e348c" translation = "5.52 8.07 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "71" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_72_037e348c" uuid = "037e348c" id = "at_72_037e348c" radius = "1.32" name = "at_037e348c" translation = "-0.98 2.81 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "72" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_73_037e348c" uuid = "037e348c" id = "at_73_037e348c" radius = "1.32" name = "at_037e348c" translation = "0.32 2.06 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "73" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_74_037e348c" uuid = "037e348c" id = "at_74_037e348c" radius = "1.32" name = "at_037e348c" translation = "1.62 1.31 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "74" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_75_037e348c" uuid = "037e348c" id = "at_75_037e348c" radius = "1.32" name = "at_037e348c" translation = "1.6 2.77 17.25" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "75" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_76_037e348c" uuid = "037e348c" id = "at_76_037e348c" radius = "1.32" name = "at_037e348c" translation = "0.32 2.06 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "76" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_77_037e348c" uuid = "037e348c" id = "at_77_037e348c" radius = "1.32" name = "at_037e348c" translation = "1.62 1.31 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "77" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_78_037e348c" uuid = "037e348c" id = "at_78_037e348c" radius = "1.32" name = "at_037e348c" translation = "-2.28 5.06 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "78" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_79_037e348c" uuid = "037e348c" id = "at_79_037e348c" radius = "1.32" name = "at_037e348c" translation = "-0.98 4.31 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "79" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_80_037e348c" uuid = "037e348c" id = "at_80_037e348c" radius = "1.32" name = "at_037e348c" translation = "0.32 3.56 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "80" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_81_037e348c" uuid = "037e348c" id = "at_81_037e348c" radius = "1.32" name = "at_037e348c" translation = "0.29 5.1 17.14" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "81" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_82_037e348c" uuid = "037e348c" id = "at_82_037e348c" radius = "1.32" name = "at_037e348c" translation = "-0.98 4.31 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "82" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_83_037e348c" uuid = "037e348c" id = "at_83_037e348c" radius = "1.32" name = "at_037e348c" translation = "0.32 3.56 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "83" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_84_037e348c" uuid = "037e348c" id = "at_84_037e348c" radius = "1.32" name = "at_037e348c" translation = "-3.58 7.32 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "84" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_85_037e348c" uuid = "037e348c" id = "at_85_037e348c" radius = "1.32" name = "at_037e348c" translation = "-2.28 6.57 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "85" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_86_037e348c" uuid = "037e348c" id = "at_86_037e348c" radius = "1.32" name = "at_037e348c" translation = "-0.98 5.81 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "86" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_87_037e348c" uuid = "037e348c" id = "at_87_037e348c" radius = "1.32" name = "at_037e348c" translation = "-0.98 7.32 17.19" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "87" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_88_037e348c" uuid = "037e348c" id = "at_88_037e348c" radius = "1.32" name = "at_037e348c" translation = "-2.28 6.57 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "88" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_89_037e348c" uuid = "037e348c" id = "at_89_037e348c" radius = "1.32" name = "at_037e348c" translation = "-0.98 5.81 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "89" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_90_037e348c" uuid = "037e348c" id = "at_90_037e348c" radius = "1.32" name = "at_037e348c" translation = "0.32 0.56 10.83" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "90" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_91_037e348c" uuid = "037e348c" id = "at_91_037e348c" radius = "1.32" name = "at_037e348c" translation = "-3.58 8.82 6.58" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "91" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_92_037e348c" uuid = "037e348c" id = "at_92_037e348c" radius = "1.32" name = "at_037e348c" translation = "-2.28 8.07 8.71" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "92" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_93_037e348c" uuid = "037e348c" id = "at_93_037e348c" radius = "1.32" name = "at_037e348c" translation = "2.93 0.54 17.16" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "93" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_94_037e348c" uuid = "037e348c" id = "at_94_037e348c" radius = "1.32" name = "at_037e348c" translation = "-3.58 8.82 12.95" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "94" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_95_037e348c" uuid = "037e348c" id = "at_95_037e348c" radius = "1.32" name = "at_037e348c" translation = "-2.28 8.07 15.08" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "95" uuid = "037e348c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_C" id = "as_C_037e348c" > 
             <Appearance DEF = "app_C" > 
               <Material name = "am_037e348c" diffuseColor = "0.565 0.565 0.565" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_C" radius = "0.76" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_98_037e348c" uuid = "037e348c" id = "at_98_037e348c" radius = "0.76" name = "at_037e348c" translation = "2.6 4.5 19.11" scale = "1 1 1" > 
           <Shape kind = "C" index = "98" uuid = "037e348c" > 
             <Appearance USE = "app_C" > 
             </Appearance> 
             <Sphere USE = "asp_C" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Group> 
       <Shape > 
         <IndexedLineSet coordIndex = "0 1 -1 0 2 -1 0 4 -1 1 3 -1 1 5 -1 2 3 -1 2 6 -1 3 7 -1 4 5 -1 4 6 -1 5 7 -1 6 7 -1" > 
           <Coordinate point = "[[ 0.00000000e+00  0.00000000e+00  0.00000000e+00  0.00000000e+00
   0.00000000e+00  3.82134074e+01 -5.20018609e+00  9.00698651e+00
   6.00000000e-16 -5.20018609e+00  9.00698651e+00  3.82134074e+01
   1.04003722e+01  0.00000000e+00  6.00000000e-16  1.04003722e+01
   0.00000000e+00  3.82134074e+01  5.20018609e+00  9.00698651e+00
   1.20000000e-15  5.20018609e+00  9.00698651e+00  3.82134074e+01]]" > 
           </Coordinate> 
         </IndexedLineSet> 
         <Appearance > 
           <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
           </Material> 
         </Appearance> 
       </Shape> 
       <Switch id = "switch_marker_0_037e348c" whichChoice = "-1" > 
         <Transform id = "marker_0_037e348c" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_1_037e348c" whichChoice = "-1" > 
         <Transform id = "marker_1_037e348c" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_2_037e348c" whichChoice = "-1" > 
         <Transform id = "marker_2_037e348c" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_3_037e348c" whichChoice = "-1" > 
         <Transform id = "marker_3_037e348c" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_4_037e348c" whichChoice = "-1" > 
         <Transform id = "marker_4_037e348c" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_line_0_037e348c" whichChoice = "-1" > 
         <Shape > 
           <IndexedLineSet id = "line_ind_0_037e348c" solid = "false" coordIndex = "0 1 -1" > 
             <Coordinate id = "line_coor_0_037e348c" point = "0 0 0 0 0 1" > 
             </Coordinate> 
           </IndexedLineSet> 
           <Appearance > 
             <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
             </Material> 
           </Appearance> 
         </Shape> 
       </Switch> 
       <Switch id = "switch_line_1_037e348c" whichChoice = "-1" > 
         <Shape > 
           <IndexedLineSet id = "line_ind_1_037e348c" solid = "false" coordIndex = "0 1 -1" > 
             <Coordinate id = "line_coor_1_037e348c" point = "0 0 0 0 0 1" > 
             </Coordinate> 
           </IndexedLineSet> 
           <Appearance > 
             <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
             </Material> 
           </Appearance> 
         </Shape> 
       </Switch> 
       <TimeSensor DEF = "time" cycleInterval = "10" loop = "true" > 
       </TimeSensor> 
       <PositionInterpolator DEF = "move_0" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 1.6201296271537546 2.8126443520296065 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_0" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_0" fromField = "value_changed" toNode = "at_0_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_1" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 2.9201761487067275 2.06206214285196 6.584500959132651 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_1" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_1" fromField = "value_changed" toNode = "at_1_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_2" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 4.220222670259701 1.311479933674316 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_2" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_2" fromField = "value_changed" toNode = "at_2_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_3" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.259788985951393 2.808094618616392 17.14004881828876 4.256858693962706 2.805547525519112 17.141166935528556 4.253070344945797 2.8032405177569983 17.143064071449533 4.248475630965201 2.801021342895866 17.14584774895831 4.243332790104514 2.7988804994258847 17.14948533476378 4.237931932911483 2.7968864097827684 17.153754557345096 4.2324988781040584 2.7951232998192297 17.158373034500915 4.227160092012714 2.7937398236499216 17.1631549707988 4.2219245296286045 2.7929450912579132 17.16806510545708 4.216748610674954 2.792826820083931 17.17311496560258 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_3" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_3" fromField = "value_changed" toNode = "at_3_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_4" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 2.920176148706727 2.0620621428519623 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_4" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_4" fromField = "value_changed" toNode = "at_4_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_5" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 4.220222670259702 1.3114799336743141 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_5" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_5" fromField = "value_changed" toNode = "at_5_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_6" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 0.3200831056007806 5.064390979562543 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_6" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_6" fromField = "value_changed" toNode = "at_6_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_7" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 1.6201296271537529 4.313808770384897 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_7" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_7" fromField = "value_changed" toNode = "at_7_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_8" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 2.9201761487067266 3.563226561207253 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_8" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_8" fromField = "value_changed" toNode = "at_8_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_9" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.958787924211506 5.117329070203434 17.267716247229032 2.9522670260463397 5.112703941847689 17.246519761831834 2.9427384712109634 5.104492252242844 17.233645053698375 2.931967872433189 5.095230153970595 17.220727668445303 2.9203423596393128 5.085703297137919 17.206811236056954 2.908832347388051 5.076298737563199 17.193573991670213 2.900075364826993 5.068271900707878 17.185019320874588 2.8970113796114108 5.063277054700762 17.184760762821526 2.9022572252631313 5.062896828696411 17.1948706760504 2.917306280284471 5.06807528772248 17.214906560858612 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_9" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_9" fromField = "value_changed" toNode = "at_9_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_10" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 1.6201296271537529 4.313808770384899 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_10" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_10" fromField = "value_changed" toNode = "at_10_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_11" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 2.920176148706727 3.5632265612072525 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_11" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_11" fromField = "value_changed" toNode = "at_11_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_12" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 -0.9799634159521938 7.31613760709548 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_12" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_12" fromField = "value_changed" toNode = "at_12_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_13" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 0.32008310560077874 6.565555397917834 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_13" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_13" fromField = "value_changed" toNode = "at_13_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_14" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 1.6201296271537524 5.81497318874019 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_14" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_14" fromField = "value_changed" toNode = "at_14_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_15" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6150952205736835 7.340481954620783 17.172382611431736 1.612727349992298 7.34029647173824 17.172231055514494 1.6105655087038797 7.339621639757671 17.172475280209 1.6084597884442047 7.338695412528436 17.17285645834915 1.606298100805935 7.3377372716401785 17.17316181138612 1.6040499045394965 7.336815092194553 17.17335595304945 1.601818117158467 7.335876907264269 17.173560938747936 1.5998367374892621 7.334790794325717 17.17396847393428 1.598404687453461 7.3333758095400245 17.174737121577174 1.5976446184073307 7.3315550506976095 17.17584802402066 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_15" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_15" fromField = "value_changed" toNode = "at_15_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_16" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 0.3200831056007782 6.565555397917838 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_16" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_16" fromField = "value_changed" toNode = "at_16_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_17" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 1.6201296271537535 5.814973188740188 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_17" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_17" fromField = "value_changed" toNode = "at_17_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_18" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 2.9201761487067293 0.5608977244966684 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_18" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_18" fromField = "value_changed" toNode = "at_18_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_19" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 -0.9799634159521963 8.817302025450772 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_19" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_19" fromField = "value_changed" toNode = "at_19_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_20" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 0.3200831056007786 8.066719816273128 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_20" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_20" fromField = "value_changed" toNode = "at_20_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_21" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.524550094223409 0.5666000592715079 17.180721978523756 5.523845501948558 0.5662335545222698 17.180754907355155 5.523081900477506 0.5658817792643979 17.180782111066232 5.522234434701645 0.5655607168179206 17.180801135098786 5.521320937781957 0.5652887940460536 17.180811938599884 5.520392533489724 0.5650819484893312 17.180815192945225 5.519517232632239 0.5649475887208482 17.1808114420252 5.5187550772861265 0.5648831196720134 17.1808010576215 5.518126338407326 0.5648772702201866 17.180781660231606 5.517591816311055 0.5649065048648777 17.18075585749683 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_21" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_21" fromField = "value_changed" toNode = "at_21_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_22" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 -0.9799634159521956 8.817302025450774 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_22" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_22" fromField = "value_changed" toNode = "at_22_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_23" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 0.3200831056007782 8.066719816273125 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_23" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_23" fromField = "value_changed" toNode = "at_23_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_24" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 4.220222670259702 2.8126443520296065 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_24" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_24" fromField = "value_changed" toNode = "at_24_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_25" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 5.520269191812674 2.06206214285196 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_25" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_25" fromField = "value_changed" toNode = "at_25_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_26" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 6.820315713365648 1.311479933674316 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_26" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_26" fromField = "value_changed" toNode = "at_26_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_27" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.832683927120229 2.8187455577345264 17.177451357526557 6.831484522887066 2.8180667201926517 17.177489433046535 6.830309633501756 2.817221908742239 17.177534439047424 6.829192697440386 2.816118953658017 17.17759039904593 6.828116392367312 2.8148420560664116 17.177650056299267 6.826972614196169 2.8137246299462975 17.177679863240343 6.825513703296496 2.813260969440906 17.177624713633275 6.823464319800043 2.8139045099202606 17.177445611085723 6.820681203482792 2.8158627097873956 17.177155038586115 6.817304138323884 2.818844623697849 17.176814181465666 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_27" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_27" fromField = "value_changed" toNode = "at_27_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_28" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 5.520269191812672 2.0620621428519623 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_28" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_28" fromField = "value_changed" toNode = "at_28_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_29" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 6.82031571336565 1.3114799336743141 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_29" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_29" fromField = "value_changed" toNode = "at_29_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_30" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 2.9201761487067275 5.064390979562543 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_30" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_30" fromField = "value_changed" toNode = "at_30_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_31" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 4.220222670259699 4.313808770384897 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_31" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_31" fromField = "value_changed" toNode = "at_31_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_32" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 5.520269191812674 3.563226561207253 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_32" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_32" fromField = "value_changed" toNode = "at_32_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_33" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.5516328350267585 5.072953908378388 17.1655574999412 5.550937840807001 5.071613174930816 17.16654866414745 5.551716294467123 5.069752718083766 17.165349498235642 5.555234231230803 5.066750524951851 17.160196870099355 5.561416589478455 5.062225437646993 17.15213117744342 5.567549102267559 5.057095919260528 17.146399593150242 5.569201498065531 5.053509512235639 17.14863407324213 5.562114086743681 5.053645935759838 17.161859102891423 5.543065433622003 5.059342464844132 17.18677092169918 5.51140539428603 5.071502902619864 17.221567827560648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_33" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_33" fromField = "value_changed" toNode = "at_33_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_34" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 4.2202226702597 4.313808770384899 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_34" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_34" fromField = "value_changed" toNode = "at_34_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_35" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 5.520269191812674 3.5632265612072525 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_35" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_35" fromField = "value_changed" toNode = "at_35_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_36" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 1.6201296271537533 7.31613760709548 10.830435118712591 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_36" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_36" fromField = "value_changed" toNode = "at_36_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_37" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 2.9201761487067253 6.565555397917834 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_37" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_37" fromField = "value_changed" toNode = "at_37_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_38" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 4.2202226702597 5.81497318874019 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_38" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_38" fromField = "value_changed" toNode = "at_38_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_39" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.23830080909136 7.353226514635221 17.165652884285706 4.236237207642013 7.35131367881661 17.169002260189586 4.234286300741711 7.350565481867824 17.17112035671844 4.232522408114673 7.352182626420734 17.170666579241722 4.230927758125793 7.356886375347935 17.166940517946664 4.229421001291327 7.363350783350253 17.161940172963966 4.227714940020821 7.36716981713111 17.161037085757027 4.22521728062447 7.362482178311439 17.169865866043796 4.221539748682669 7.34435276309034 17.19121927883353 4.217027631335194 7.311582597892179 17.223481350792028 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_39" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_39" fromField = "value_changed" toNode = "at_39_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_40" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 2.920176148706723 6.565555397917838 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_40" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_40" fromField = "value_changed" toNode = "at_40_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_41" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 4.2202226702597 5.814973188740188 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_41" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_41" fromField = "value_changed" toNode = "at_41_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_42" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 5.520269191812677 0.5608977244966684 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_42" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_42" fromField = "value_changed" toNode = "at_42_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_43" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 1.6201296271537504 8.817302025450772 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_43" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_43" fromField = "value_changed" toNode = "at_43_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_44" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 2.9201761487067253 8.066719816273128 8.70746803892262 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_44" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_44" fromField = "value_changed" toNode = "at_44_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_45" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "8.118949606548945 0.5761077974052543 17.176214748268954 8.118613646326514 0.57517461341147 17.17641734596254 8.118206323434208 0.5743871910766423 17.176632707742666 8.117657822580105 0.5738713994701808 17.176866870019325 8.11698206252009 0.5736162329056493 17.177109217145805 8.116321384132865 0.5733662491693646 17.1773259492401 8.115961346924713 0.5726289224809312 17.17746582542693 8.11622197987181 0.5708544285515811 17.177487050091944 8.117277139703342 0.5677249690786561 17.177394327039906 8.118957673151554 0.5634987438915893 17.177246574327764 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_45" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_45" fromField = "value_changed" toNode = "at_45_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_46" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 1.620129627153749 8.817302025450774 12.95340219850257 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_46" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_46" fromField = "value_changed" toNode = "at_46_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_47" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 2.9201761487067257 8.066719816273125 15.076369278292537 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_47" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_47" fromField = "value_changed" toNode = "at_47_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_48" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 6.820315713365648 2.8126443520296065 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_48" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_48" fromField = "value_changed" toNode = "at_48_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_49" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 8.12036223491862 2.06206214285196 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_49" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_49" fromField = "value_changed" toNode = "at_49_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_50" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 9.420408756471595 1.311479933674316 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_50" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_50" fromField = "value_changed" toNode = "at_50_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_51" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-1.003670097976923 2.8153878983697185 17.164349133677955 -1.002274859841078 2.8153751468468053 17.166180800621635 -1.000449459333417 2.815379907498849 17.168032731604786 -0.9982032974840139 2.8154090329779735 17.169889101647197 -0.9957720236444264 2.815496897789472 17.171733920656298 -0.9933073870772318 2.815632566649875 17.173561102426287 -0.9908563062116309 2.8158152622526904 17.175372851902324 -0.9883995438473983 2.8160440217005305 17.17717118400779 -0.9859088952839822 2.816311671869503 17.17895730908792 -0.9833842714448553 2.8166030726096105 17.180735094634105 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_51" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_51" fromField = "value_changed" toNode = "at_51_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_52" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 8.120362234918622 2.0620621428519623 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_52" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_52" fromField = "value_changed" toNode = "at_52_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_53" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 9.420408756471597 1.3114799336743141 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_53" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_53" fromField = "value_changed" toNode = "at_53_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_54" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 5.520269191812676 5.064390979562543 10.830435118712591 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_54" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_54" fromField = "value_changed" toNode = "at_54_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_55" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 6.820315713365646 4.313808770384897 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_55" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_55" fromField = "value_changed" toNode = "at_55_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_56" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 8.120362234918623 3.563226561207253 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_56" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_56" fromField = "value_changed" toNode = "at_56_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_57" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.273969963203836 5.075493368840874 17.18801608731615 -2.2746464576167793 5.0745371693252785 17.186904406566597 -2.274868499399078 5.073552389249645 17.18581473013379 -2.2745318398862664 5.072533232889866 17.184751955184502 -2.273902398014121 5.0714947830759405 17.183692486348022 -2.2735280819544967 5.0704538263318675 17.18258450595365 -2.2740717398844157 5.069418213414997 17.1813634542938 -2.2760809434141587 5.068388207460276 17.179991601484062 -2.279761168988204 5.067366179019517 17.178492681306746 -2.284700632427861 5.066357642720443 17.17694104651217 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_57" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_57" fromField = "value_changed" toNode = "at_57_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_58" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 6.820315713365647 4.313808770384899 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_58" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_58" fromField = "value_changed" toNode = "at_58_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_59" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 8.120362234918622 3.5632265612072525 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_59" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_59" fromField = "value_changed" toNode = "at_59_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_60" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 4.2202226702597 7.31613760709548 10.830435118712591 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_60" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_60" fromField = "value_changed" toNode = "at_60_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_61" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 5.520269191812672 6.565555397917834 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_61" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_61" fromField = "value_changed" toNode = "at_61_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_62" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 6.8203157133656465 5.81497318874019 8.70746803892262 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_62" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_62" fromField = "value_changed" toNode = "at_62_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_63" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.570258420626211 7.323411819534487 17.177389981461047 -3.569128557373419 7.32448201530466 17.176861868370743 -3.567937935887965 7.325664603436095 17.176297791314443 -3.5666255943370806 7.326982831366536 17.175651690216874 -3.5651733358242983 7.328363360044147 17.174898282402243 -3.5637084246978223 7.329677859368744 17.174120293748317 -3.5624699244256224 7.33077916258089 17.173457002502587 -3.561693212255892 7.331566129859249 17.172989941985993 -3.5616271098156567 7.331930687658532 17.172794423188964 -3.5624520483245004 7.331783319524079 17.172921847842925 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_63" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_63" fromField = "value_changed" toNode = "at_63_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_64" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 5.520269191812671 6.565555397917838 12.95340219850257 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_64" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_64" fromField = "value_changed" toNode = "at_64_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_65" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 6.820315713365647 5.814973188740188 15.076369278292537 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_65" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_65" fromField = "value_changed" toNode = "at_65_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_66" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 8.120362234918623 0.5608977244966684 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_66" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_66" fromField = "value_changed" toNode = "at_66_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_67" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 4.220222670259697 8.817302025450772 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_67" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_67" fromField = "value_changed" toNode = "at_67_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_68" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 5.520269191812672 8.066719816273128 8.707468038922622 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_68" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_68" fromField = "value_changed" toNode = "at_68_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_69" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3118842478912281 0.5580828427466905 17.165508073583833 0.31159524385556736 0.5579957529884649 17.166722086035023 0.31185985982595343 0.5587935845122393 17.1679834091038 0.3127526786424716 0.560586819668144 17.169312376431666 0.313982829767446 0.562948237560594 17.170722652194577 0.3151740327706324 0.5652878871249192 17.17213364968292 0.3159474834787285 0.5669224181914676 17.173461705515045 0.3159588518105167 0.567238483832258 17.174649608283257 0.3150605916825622 0.565966453882981 17.175700627890432 0.31348227841018333 0.5634905760253299 17.176682299116194 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_69" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_69" fromField = "value_changed" toNode = "at_69_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_70" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 4.220222670259699 8.817302025450774 12.95340219850257 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_70" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_70" fromField = "value_changed" toNode = "at_70_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_71" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 5.520269191812673 8.066719816273125 15.076369278292537 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_71" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_71" fromField = "value_changed" toNode = "at_71_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_72" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 -0.9799634159521908 2.8126443520296065 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_72" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_72" fromField = "value_changed" toNode = "at_72_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_73" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 0.3200831056007817 2.06206214285196 6.584500959132647 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_73" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_73" fromField = "value_changed" toNode = "at_73_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_74" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 1.6201296271537564 1.311479933674316 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_74" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_74" fromField = "value_changed" toNode = "at_74_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_75" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.598432103651343 2.771617841564511 17.24565190477618 1.5950701413746384 2.7685003552234395 17.222606968314413 1.59520069977533 2.7711597956213763 17.209987091820892 1.5977130247377684 2.77767380359846 17.20560643122378 1.600633814148882 2.7844908730933713 17.20379488721599 1.6032750430877136 2.7905977918030818 17.201121673297322 1.6057322211640246 2.7964410139721685 17.19680319364393 1.6084674994296824 2.802838996013133 17.191009368248828 1.6118221518786475 2.810326508714387 17.18419449625006 1.6157528957134897 2.8187964509426804 17.17687608622652 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_75" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_75" fromField = "value_changed" toNode = "at_75_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_76" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 0.3200831056007804 2.0620621428519623 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_76" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_76" fromField = "value_changed" toNode = "at_76_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_77" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 1.620129627153755 1.3114799336743141 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_77" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_77" fromField = "value_changed" toNode = "at_77_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_78" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 -2.2800099375051657 5.064390979562543 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_78" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_78" fromField = "value_changed" toNode = "at_78_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_79" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 -0.9799634159521928 4.313808770384897 6.584500959132647 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_79" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_79" fromField = "value_changed" toNode = "at_79_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_80" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 0.3200831056007819 3.563226561207253 8.707468038922622 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_80" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_80" fromField = "value_changed" toNode = "at_80_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_81" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.29468933916308093 5.096778716129049 17.142783955833536 0.2934345832193319 5.094342743594641 17.14327311839158 0.29443598560843287 5.0912030677585145 17.146282557790244 0.29671899476166447 5.087453793735726 17.151453036142016 0.29893886018612936 5.083588223838491 17.157108886354717 0.30103127708471056 5.079869202653319 17.162337701474243 0.3036767502658357 5.076322004011227 17.167010660771417 0.3076027608945015 5.072901379016975 17.17120201304137 0.3131730429263791 5.069554965232656 17.175025156168754 0.3200493953827482 5.066245765940367 17.1786507797658 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_81" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_81" fromField = "value_changed" toNode = "at_81_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_82" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 -0.9799634159521937 4.313808770384899 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_82" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_82" fromField = "value_changed" toNode = "at_82_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_83" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 0.3200831056007827 3.5632265612072525 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_83" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_83" fromField = "value_changed" toNode = "at_83_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_84" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 -3.58005645905814 7.31613760709548 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_84" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_84" fromField = "value_changed" toNode = "at_84_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_85" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 -2.280009937505167 6.565555397917834 6.584500959132648 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_85" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_85" fromField = "value_changed" toNode = "at_85_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_86" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 -0.9799634159521925 5.81497318874019 8.707468038922622 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_86" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_86" fromField = "value_changed" toNode = "at_86_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_87" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9771239534695154 7.321020153512626 17.188761949851393 -0.9778027306270389 7.320944698092064 17.18797045170144 -0.9783983240291853 7.32079960495895 17.18716952731768 -0.9789166836898457 7.320600648055746 17.186358788717655 -0.9794063914853092 7.3203819016638825 17.18554083062808 -0.9799044703912585 7.320123218174471 17.18471448952991 -0.9804210771176809 7.319760402234505 17.18387524613922 -0.9809454290008673 7.319232070587721 17.183018970979155 -0.981465213887646 7.318523573939327 17.18214583566879 -0.9819819366060177 7.317693085293363 17.181262158528433 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_87" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_87" fromField = "value_changed" toNode = "at_87_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_88" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 -2.2800099375051692 6.565555397917838 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_88" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_88" fromField = "value_changed" toNode = "at_88_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_89" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 -0.9799634159521913 5.814973188740188 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_89" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_89" fromField = "value_changed" toNode = "at_89_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_90" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 0.32008310560078335 0.5608977244966684 10.83043511871259 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_90" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_90" fromField = "value_changed" toNode = "at_90_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_91" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 -3.5800564590581447 8.817302025450772 6.58450095913265 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_91" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_91" fromField = "value_changed" toNode = "at_91_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_92" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 -2.2800099375051674 8.066719816273128 8.707468038922624 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_92" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_92" fromField = "value_changed" toNode = "at_92_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_93" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.9298898575601426 0.5436100471350247 17.158874348813438 2.9287009560395827 0.5450540834498594 17.16093024435911 2.927288193532633 0.5469913300080795 17.163178943679792 2.9256825222592515 0.5494090776864081 17.165661099951883 2.924025587981684 0.5520282000428488 17.168293818910605 2.9223662028839517 0.5546319352656841 17.170960575587497 2.9206557623985745 0.5571845337577856 17.173585666345048 2.918830234383146 0.5597278643180091 17.17614623239661 2.9168618424207247 0.5623045419579763 17.178651365612467 2.914788356205876 0.5649149459211363 17.181125000140707 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_93" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_93" fromField = "value_changed" toNode = "at_93_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_94" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 -3.580056459058143 8.817302025450774 12.953402198502568 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_94" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_94" fromField = "value_changed" toNode = "at_94_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_95" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "-2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 -2.280009937505168 8.066719816273125 15.076369278292535 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_95" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_95" fromField = "value_changed" toNode = "at_95_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_96" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "3.0361483069584887 5.20007405002636 20.19279303332487 2.6780848835477142 5.449530672531527 20.331019706191228 2.5184717411902415 5.525631421985019 20.591597285414995 2.4939696436260714 5.534196399685909 20.83433890441171 2.5204520431957453 5.522088124650274 21.03216978968264 2.5579577711168198 5.505260255740813 21.191170897595583 2.590439751885082 5.489483256823346 21.321711184074587 2.6121806666771117 5.475270592562685 21.435186298259957 2.6237884527872026 5.4604516088710255 21.54125611736721 2.6345380022348195 5.43384028729841 21.642802174291386 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_96" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_96" fromField = "value_changed" toNode = "at_96_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_97" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "1.9502278733964309 3.436180216297302 19.305458682638857 2.1469345213061177 3.286752414379508 19.554336621445344 2.3617032562802676 3.183367126012809 19.7935995681573 2.58781755058075 3.1347658341820797 20.023420636706533 2.8177190398635745 3.1345259347861987 20.248030897880717 3.0475412766685186 3.1707881956822486 20.47050366303192 3.2752593764445064 3.231275520485618 20.691645923814153 3.49923197095807 3.3059178981759123 20.911815755098697 3.718298377725511 3.3885591448620946 21.132218509276377 3.9319718470150327 3.477949508369305 21.35395285210702 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_97" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_97" fromField = "value_changed" toNode = "at_97_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_98" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.600093043105944 4.503493255065873 19.106703718109742 2.4488462148952177 4.450584456935292 19.286337261410957 2.3377926575175287 4.380724254325083 19.545059866380626 2.323324034719018 4.316349859543243 19.873067128796997 2.41213696330107 4.28243974886939 20.21757943792582 2.571574427076089 4.285602984648384 20.536311948246546 2.760458327986574 4.317054050361564 20.813159127693808 2.9483785697726117 4.361574851741232 21.054989001600834 3.122795056269899 4.4065771814576875 21.2788613247934 3.283436387670884 4.4557743064382835 21.49976359872498 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_98" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_98" fromField = "value_changed" toNode = "at_98_037e348c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_99" key = " 0.0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9" keyValue = "2.73736823277318 4.712565147695313 21.000030423760517 3.4705800401262716 4.819551544601533 20.85644426905328 3.941904463248689 5.067281973372721 20.49697670044486 4.163782144193742 5.317074119287894 20.08500834828014 4.224854385008601 5.50589848890159 19.703816943584222 4.205242941494631 5.624689996741726 19.378029204622024 4.166517021255594 5.6902913796191985 19.09180677455317 4.147878492888985 5.729684608290208 18.80859840645337 4.162866957484878 5.7652931400330525 18.494412790510946 4.201603867873281 5.804965402064039 18.141274260641893 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_99" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_99" fromField = "value_changed" toNode = "at_99_037e348c" toField = "translation" > 
       </ROUTE> 
     </Scene> 
   </X3D> 
 </div> 

<script type="text/javascript" src="https://code.jquery.com/jquery-2.1.0.min.js" ></script>
 <script >
 if (atoms_dict == undefined) {var atoms_dict = {"new": true};}; 
atoms_dict["037e348c"] = {com: "2.70 4.69 11.95", 
top_pos: "2.70 4.69 50.95", 
front_pos: "2.70 -34.31 11.95", 
right_pos: "41.70 4.69 11.95", 
left_pos: "-36.30 4.69 11.95", 
top_ori: "0 0 0 0", 
front_ori: "1 0 0 1.57079", 
right_ori: "0 1 0 1.57079", 
left_ori: "0 1 0 -1.57079", 
select: [], 
uuid: "037e348c", 
label: "False", 
bond: "False", 
polyhedra: {}, 
};

// if (atoms_dict[uuid]['new']){
function setQuality(uuid, quality){
        // $('#error_'.concat(atoms_dict[uuid]['uuid'])).html('uuid: '.concat(atoms_dict[uuid]["uuid"]));
        var x3d = 'x3dase_' + uuid;
        document.getElementById(x3d).setAttribute('PrimitiveQuality', quality);
    }
function set_viewpoint(uuid, pos, ori){
    // $('#error_'.concat(atoms_dict[uuid]['uuid'])).html('uuid: '.concat(atoms_dict[uuid]["uuid"]));
    var persp = 'camera_persp_' + uuid;
    var ortho = 'camera_ortho_' + uuid;
    document.getElementById(persp).setAttribute('orientation', atoms_dict[uuid][ori]);
    document.getElementById(persp).setAttribute('position', atoms_dict[uuid][pos]);
    document.getElementById(ortho).setAttribute('orientation', atoms_dict[uuid][ori]);
    document.getElementById(ortho).setAttribute('position', atoms_dict[uuid][pos]);
}

//Round a float value to x.xx format
function roundWithTwoDecimals(value)
{
    var x = (Math.round(value * 100)) / 100;
    var y = x.toFixed(2);
    return y;
}
//Handle click on any group member
function handleGroupClick(event)
{
    //Mark hitting point
    var target = event.target;
    var uuid = target.parentNode.getAttribute('uuid')
    var radius = target.parentNode.getAttribute('radius');
    var scale = target.parentNode.getAttribute('scale');
    scale = parseFloat(radius)*parseFloat(scale)*1.2;
    var scale = ' ' + scale + ' ' + scale + ' ' + scale;
    var translation = target.parentNode.getAttribute('translation');
    var id = target.parentNode.getAttribute('id');
    if (window.event.ctrlKey) {
        atoms_dict[uuid]['select'].push(id);

    }
    else {
        for (var i=1; i<= atoms_dict[uuid]['select'].length; i++) {
            $('#switch_marker_' + i + '_' + uuid).attr('whichChoice', -1);
        }
        atoms_dict[uuid]['select'] = [];
        atoms_dict[uuid]['select'].push(id);
        $('#switch_marker_' + 2 + '_' + uuid).attr('whichChoice', -1);
}
    var n = atoms_dict[uuid]['select'].length;
    $('#switch_marker_' + n + '_' + uuid).attr('whichChoice', 0);
    $('#marker_' + n + '_' + uuid).attr('translation', translation);
    $('#marker_' + n + '_' + uuid).attr('scale', scale);
    var atom_kind = '#lastonMouseoverObject_kind_'.concat(uuid);
    var atom_index = '#lastonMouseoverObject_index_'.concat(uuid);
    $(atom_kind).html(target.getAttribute("kind"));
    $(atom_index).html(target.getAttribute("index"));
    //
    var coord = translation.split(" ")
    var atom_position = '#position_'.concat(uuid);
    var x = roundWithTwoDecimals(coord[0]);
    var y = roundWithTwoDecimals(coord[1]);
    var z = roundWithTwoDecimals(coord[2]);
    var position = 'x = ' + x + ' y = ' + y + ' z = ' + z;
    $(atom_position).html(position);

    if (atoms_dict[uuid]['select'].length == 2){
        calculate_distance(uuid);
        draw_line(uuid);
    }
    else if (atoms_dict[uuid]['select'].length == 3){
        calculate_angle(uuid);
        draw_line(uuid);
    }

    console.log(event);
}
//Add a onMouseover callback to every shape
$(document).ready(function(){
    $("shape").each(function() {
        // $(this).attr("onMouseover", "handleOnMouseover_shape(this)");
        // $(this).attr("onclick", "handleClick_shape(this)");
    });
    //Add a onMouseover callback to every transform
    $("transform").each(function() {
        // $(this).attr("onMouseover", "handleOnMouseover_transform(this)");
        // $(this).attr("onclick", "handleClick_transform(this)");
    });
});
$(document).on("click", function(e) {
    if (e.target === document || e.target.tagName === "BODY" || e.target.tagName === "HTML") {
        $('#marker').attr('scale', "0.0001 0.0001 0.0001");
    }
});
//Handle onMouseover on a shape
function handleOnMouseover_shape(shape)
{
    var atom_kind = '#lastonMouseoverObject_kind_'.concat($(shape).attr("uuid"));
    var atom_index = '#lastonMouseoverObject_index_'.concat($(shape).attr("uuid"));
    $(atom_kind).html($(shape).attr("kind"));
    $(atom_index).html($(shape).attr("index"));
}
//Handle onMouseover on a shape
function handleClick_shape(shape)
{
    var atom_kind = '#lastonMouseoverObject_kind_'.concat($(shape).attr("uuid"));
    var atom_index = '#lastonMouseoverObject_index_'.concat($(shape).attr("uuid"));
    $(atom_kind).html($(shape).attr("kind"));
    $(atom_index).html($(shape).attr("index"));
}
//Handle onMouseover on a transform
function handleOnMouseover_transform(transform)
{
    var atom_position = '#position_'.concat($(transform).attr("uuid"));
    var coord = $(transform).attr("translation").split(" "[0]);
    var x = roundWithTwoDecimals(coord[0]);
    var y = roundWithTwoDecimals(coord[1]);
    var z = roundWithTwoDecimals(coord[2]);
    var position = 'x = ' + x + ' y = ' + y + ' z = ' + z;
    $(atom_position).html(position);
}

function calculate_distance(uuid)
{
    var measure = '#measure_'.concat(uuid);
    var c1 = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation").split(" ");
    var c2 = document.getElementById(atoms_dict[uuid]['select'][1]).getAttribute("translation").split(" ");
    r = (c1[0] - c2[0])*(c1[0] - c2[0]) + (c1[1] - c2[1])*(c1[1] - c2[1]) + (c1[2] - c2[2])*(c1[2] - c2[2]);
    r = roundWithTwoDecimals(Math.sqrt(r));
    var dist = 'Distance:  ' + r;
    $(measure).html(dist);
}
function calculate_angle(uuid)
{
    var measure = '#measure_'.concat(uuid);
    var c1 = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation").split(" ");
    var c2 = document.getElementById(atoms_dict[uuid]['select'][1]).getAttribute("translation").split(" ");
    var c3 = document.getElementById(atoms_dict[uuid]['select'][2]).getAttribute("translation").split(" ");
    var AB = Math.sqrt(Math.pow(c2[0]-c1[0],2)+ Math.pow(c2[1]-c1[1],2) + Math.pow(c2[2]-c1[2],2));    
    var BC = Math.sqrt(Math.pow(c2[0]-c3[0],2)+ Math.pow(c2[1]-c3[1],2) + Math.pow(c2[2]-c3[2],2)); 
    var AC = Math.sqrt(Math.pow(c3[0]-c1[0],2)+ Math.pow(c3[1]-c1[1],2)+ Math.pow(c3[2]-c1[2],2));
    var angle = roundWithTwoDecimals(Math.acos((BC*BC+AB*AB-AC*AC)/(2*BC*AB))*180/3.1415926);
    var angle = 'angle:  ' + angle;
    $(measure).html(angle);
}
function draw_line(uuid)
{
    var n = atoms_dict[uuid]['select'].length;
    var coordIndex = '';
    var point = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation");
    for (var i = 1; i < n; i++) {
        var c1 = document.getElementById(atoms_dict[uuid]['select'][i]).getAttribute("translation");
        coordIndex = coordIndex + (i-1) + ' ' + i + ' -1 ';
        point = point + ' ' + c1 + ' ';
    }
    $('#line_coor_' + 0 + '_' + uuid).attr('point', point);
    $('#line_ind_' + 0 + '_' + uuid).attr('coordIndex', coordIndex);
    $('#switch_line_' + 0 + '_' + uuid).attr('whichChoice', 0);
}
//Handle models
function spacefilling(uuid)
{
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "1.0, 1.0, 1.0");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function ballstick(uuid)
{
    if (atoms_dict[uuid]['bond']=='False'){ 
        alert('Please set bond parameter in your code, e.g. bond=1.0!');
        $('#error_'.concat(uuid)).html('(^_^) Please set bond parameter in your code, e.g. bond=1.0!');
		return ;
    }
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "0.6, 0.6, 0.6");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function polyhedra(uuid)
{
    if (atoms_dict[uuid]['polyhedra'].length==0){ 
        alert('Please set polyhedra parameter in your code, e.g. polyhedra={"Ti": ["O"]}!');
        $('#error_'.concat(uuid)).html('(^_^) Please set polyhedra parameter in your code, e.g. polyhedra={"Ti": ["O"]}!');
		return ;
    }
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "0.6, 0.6, 0.6");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '0');
}
function none(uuid)
{
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.0");
        }
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
        
function element(uuid)
{
    if (atoms_dict[uuid]['label']=='False'){ 
        alert('To show element, please set label=True in your code!');
        $('#error_'.concat(uuid)).html('(^_^) To show element, please set label=True in your code!');
		return ;
	}
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.4");
        }
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function index(uuid)
{
    if (atoms_dict[uuid]['label']=='False'){ 
        alert('To show index, please set label=True in your code!');
        $('#error_'.concat(uuid)).html('(^_^) To show index, please set label=True in your code!');
		return ;
	}
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.4");
        }
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
// } 
</script> </body>
</html>
