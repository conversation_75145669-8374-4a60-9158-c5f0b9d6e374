#!/usr/bin/env python3
"""
优化的NEB计算脚本
=================

功能:
1. 加载初态和终态结构
2. 分析结构信息和周期边界条件
3. 比较原子位置变化
4. 执行NEB计算
5. 生成可视化文件和能量图

使用方法:
python neb.py [reaction_label] [--trajectory-file TRAJ_FILE] [--final-file FINAL_FILE]

参数:
- reaction_label: 反应标签 (默认: r11)
  脚本会自动查找 data/{reaction_label}/ini.vasp 和 data/{reaction_label}/fin.vasp
- --trajectory-file, --traj: 外部轨迹文件路径 (可选)
  如果提供，将使用该文件中的帧而非内置插值算法
- --final-file: 自定义终态文件路径 (默认: data/{reaction_label}/fin.vasp)

模型配置:
- 使用本地预下载的检查点文件 (uma_sm.pt)
- 可以通过修改 checkpoint_path 参数使用其他本地模型文件
- 支持 FAIRChem 格式的所有预训练模型

输出文件:
- neb_analysis_report.txt: 详细分析报告
- data/{reaction_label}/interpolated_frames.html: 插值帧可视化
- data/{reaction_label}/{reaction_label}.traj: NEB轨迹文件
- data/{reaction_label}/reaction_coordinate.png: 反应坐标图
- data/{reaction_label}/{reaction_label}_neb.html: 优化后NEB可视化

示例:
python neb.py r11  # 分析 data/r11/ini.vasp 和 data/r11/fin.vasp (使用内置插值)
python neb.py r25  # 分析 data/r25/ini.vasp 和 data/r25/fin.vasp (使用内置插值)
python neb.py r11 --traj my_trajectory.traj  # 使用外部轨迹文件
python neb.py r11 --trajectory-file data/r11/custom_path.traj  # 使用完整路径的外部轨迹文件
"""

import os
import sys
import argparse
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from ase.optimize import BFGS
from ase.io import read
from fairchem.applications.cattsunami.core.autoframe import interpolate
from fairchem.core import FAIRChemCalculator
from fairchem.core.units.mlip_unit import load_predict_unit
from ase.mep import DyNEB
from pymatgen.io.ase import AseAtomsAdaptor
from pymatgen.analysis.structure_matcher import StructureMatcher
from x3dase.x3d import X3D



class NEBAnalyzer:
    """NEB计算和分析类"""
    
    def __init__(self, initial_file, final_file, output_dir, reaction_label, trajectory_file=None,
                 fmax=0.05, delta_fmax_climb=0.4, k=1.0, num_frames=10, cpu=False, 
                 checkpoint_path="uma_sm.pt", max_steps_phase1=200, max_steps_phase2=300, 
                 random_seed=None):
        """
        初始化NEB分析器
        
        Args:
            initial_file: 初态结构文件路径
            final_file: 终态结构文件路径
            output_dir: 输出目录
            reaction_label: 反应标签 (用于文件命名)
            trajectory_file: 外部轨迹文件路径 (可选)
            fmax: 力收敛标准 (eV/Å)
            delta_fmax_climb: 攀爬图像的力阈值 (eV/Å)
            k: 弹簧常数
            num_frames: 插值帧数
            cpu: 是否使用CPU而非GPU            checkpoint_path: 模型检查点文件路径
            max_steps_phase1: 第一阶段优化最大步数
            max_steps_phase2: 第二阶段优化最大步数
            random_seed: 随机种子，用于确保结果可重现 (None表示使用随机种子)
        """
        self.initial_file = initial_file
        self.final_file = final_file
        self.output_dir = output_dir
        self.reaction_label = reaction_label
        self.trajectory_file = trajectory_file
        
        # 根据是否使用外部轨迹文件设置不同的报告文件名
        if trajectory_file:
            # 提取轨迹文件名（不含路径和扩展名）
            traj_basename = os.path.splitext(os.path.basename(trajectory_file))[0]
            self.report_file = f"neb_analysis_report_{reaction_label}_{traj_basename}.txt"
        else:
            self.report_file = f"neb_analysis_report_{reaction_label}.txt"
        
        # NEB参数 - 从命令行参数获取
        self.fmax = fmax  # eV/Å
        self.delta_fmax_climb = delta_fmax_climb
        self.k = k
        self.num_frames = num_frames
        self.cpu = cpu
        self.max_steps_phase1 = max_steps_phase1
        self.max_steps_phase2 = max_steps_phase2
        # 处理随机种子 - 如果没有指定则生成一个
        import random
        import torch
        import numpy as np
        
        if random_seed is None:
            # 生成随机种子
            self.random_seed = random.randint(0, 2**32 - 1)
            self.seed_source = "随机生成"
        else:
            self.random_seed = random_seed
            self.seed_source = "用户指定"
        
        # 设置随机种子以确保结果可重现
        random.seed(self.random_seed)
        torch.manual_seed(self.random_seed)
        np.random.seed(self.random_seed)
        
        # 本地模型路径 - 从命令行参数获取
        self.checkpoint_path = checkpoint_path
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 初始化报告文件
        self.report = []
        self._add_header()
        self._write_report()  # 立即写入头部信息
    
    def _write_report(self, additional_text=None):
        """实时写入报告到文件"""
        try:
            # 如果有额外的文本，临时添加到报告中
            if additional_text is not None:
                self.report.append(additional_text)
            
            with open(self.report_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.report))
                f.write('\n')
        except Exception as e:
            print(f"警告: 无法写入报告文件 {self.report_file}: {e}")
    
    def _add_header(self):
        """添加报告头部信息"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.report.append("="*80)
        self.report.append("NEB (Nudged Elastic Band) 计算分析报告")
        self.report.append("="*80)
        self.report.append(f"生成时间: {current_time}")
        self.report.append(f"随机种子: {self.random_seed} ({self.seed_source})")
        
        if self.trajectory_file:
            # 使用外部轨迹文件时的信息
            self.report.append(f"计算模式: 外部轨迹文件")
            self.report.append(f"轨迹文件: {self.trajectory_file}")
            self.report.append(f"输出目录: {self.output_dir}")
            self.report.append("")
            self.report.append("参考文件 (用于验证轨迹首末帧一致性):")
            self.report.append(f"  初态参考: {self.initial_file}")
            self.report.append(f"  终态参考: {self.final_file}")
            self.report.append("")
            self.report.append("注意: 在外部轨迹模式下，初终态文件仅用于:")
            self.report.append("  1. 验证轨迹文件首末帧与预期结构是否一致")
            self.report.append("  2. 进行Cu原子结构匹配检查")
            self.report.append("  3. 实际NEB计算完全基于轨迹文件中的结构")
        else:
            # 使用内置插值时的信息
            self.report.append(f"计算模式: 内置插值算法")
            self.report.append(f"初态文件: {self.initial_file}")
            self.report.append(f"终态文件: {self.final_file}")
            self.report.append(f"输出目录: {self.output_dir}")
            self.report.append("")
            self.report.append("注意: 在内置插值模式下，NEB路径完全基于初终态文件生成")
        
        self.report.append("")
    
    def _add_section(self, title):
        """添加章节标题"""
        self.report.append("="*80)
        self.report.append(title)
        self.report.append("="*80)
        self.report.append("")
    
    def _add_subsection(self, title):
        """添加子章节标题"""
        self.report.append("-"*60)
        self.report.append(title)
        self.report.append("-"*60)
        self.report.append("")
    
    def _create_calculator(self):
        """创建FAIRChem计算器"""
        try:
            predictor = load_predict_unit(self.checkpoint_path)
            calc = FAIRChemCalculator(predictor, task_name="oc20")
            return calc
        except Exception as e:
            self.report.append(f"✗ 计算器创建失败: {e}")
            self._write_report()
            raise
    
    def load_structures(self):
        """加载初态和终态结构"""
        print("正在加载结构文件...")
        try:
            self.initial_frame = read(self.initial_file)
            self.final_frame = read(self.final_file)

            self.initial_frame.wrap()
            self.final_frame.wrap()
            
            self._add_section("1. 结构文件加载")
            self.report.append("✓ 结构文件加载成功")
            self.report.append(f"  初态原子数: {len(self.initial_frame)}")
            self.report.append(f"  终态原子数: {len(self.final_frame)}")
            self.report.append("")
            self._write_report()  # 实时写入
            
        except Exception as e:
            self.report.append(f"✗ 结构文件加载失败: {e}")
            self._write_report()  # 实时写入错误信息
            raise
    
    def analyze_pbc(self):
        """分析周期边界条件"""
        self._add_section("2. 周期边界条件 (PBC) 分析")
        
        # 初态信息
        self.report.append("初态结构:")
        self.report.append(f"  PBC设置: {self.initial_frame.get_pbc()}")
        cell_init = self.initial_frame.get_cell()
        self.report.append("  晶胞参数 (Å):")
        self.report.append(f"    a = [{cell_init[0][0]:>8.4f}, {cell_init[0][1]:>8.4f}, {cell_init[0][2]:>8.4f}]")
        self.report.append(f"    b = [{cell_init[1][0]:>8.4f}, {cell_init[1][1]:>8.4f}, {cell_init[1][2]:>8.4f}]")
        self.report.append(f"    c = [{cell_init[2][0]:>8.4f}, {cell_init[2][1]:>8.4f}, {cell_init[2][2]:>8.4f}]")
        self.report.append(f"  晶胞体积: {self.initial_frame.get_volume():.4f} Å³")
        self.report.append("")
        
        # 终态信息
        self.report.append("终态结构:")
        self.report.append(f"  PBC设置: {self.final_frame.get_pbc()}")
        cell_final = self.final_frame.get_cell()
        self.report.append("  晶胞参数 (Å):")
        self.report.append(f"    a = [{cell_final[0][0]:>8.4f}, {cell_final[0][1]:>8.4f}, {cell_final[0][2]:>8.4f}]")
        self.report.append(f"    b = [{cell_final[1][0]:>8.4f}, {cell_final[1][1]:>8.4f}, {cell_final[1][2]:>8.4f}]")
        self.report.append(f"    c = [{cell_final[2][0]:>8.4f}, {cell_final[2][1]:>8.4f}, {cell_final[2][2]:>8.4f}]")
        self.report.append(f"  晶胞体积: {self.final_frame.get_volume():.4f} Å³")
        self.report.append("")
        
        # 晶胞比较
        cell_diff = np.abs(cell_init - cell_final)
        max_cell_diff = np.max(cell_diff)
        self.report.append("晶胞比较:")
        self.report.append(f"  最大差异: {max_cell_diff:.6f} Å")
        self.report.append(f"  晶胞是否相同: {'是' if max_cell_diff < 1e-6 else '否'}")
        self.report.append("")
        self._write_report()  # 实时写入
    
    def analyze_atomic_positions(self):
        """分析原子位置变化"""
        self._add_section("3. 原子位置变化分析")
        
        symbols_init = self.initial_frame.get_chemical_symbols()
        positions_init = self.initial_frame.get_positions()
        symbols_final = self.final_frame.get_chemical_symbols()
        positions_final = self.final_frame.get_positions()
        
        # 基本信息
        self.report.append("基本统计:")
        self.report.append(f"  原子总数: {len(symbols_init)} (初态) vs {len(symbols_final)} (终态)")
        self.report.append(f"  原子顺序相同: {'是' if symbols_init == symbols_final else '否'}")
        self.report.append("")
        
        # 元素统计
        from collections import Counter
        element_count_init = Counter(symbols_init)
        element_count_final = Counter(symbols_final)
        
        self.report.append("元素组成:")
        for element in sorted(set(symbols_init + symbols_final)):
            init_count = element_count_init.get(element, 0)
            final_count = element_count_final.get(element, 0)
            self.report.append(f"  {element}: {init_count} (初态) vs {final_count} (终态)")
        self.report.append("")
        
        # 计算位移 (考虑周期边界条件)
        distances = []
        pbc_distances = []
        large_movements = []
        large_pbc_movements = []
        
        # 获取晶胞信息用于PBC计算
        initial_cell = self.initial_frame.get_cell()
        
        for i in range(len(symbols_init)):
            if i < len(positions_final):
                # 直接距离
                direct_distance = np.linalg.norm(positions_final[i] - positions_init[i])
                distances.append(direct_distance)
                
                # PBC感知距离
                pbc_distance, pbc_diff = self._calculate_pbc_distance(
                    positions_init[i], positions_final[i], initial_cell
                )
                pbc_distances.append(pbc_distance)
                
                # 记录大位移原子
                if direct_distance > 1.0:  # 大于1Å的直接位移
                    large_movements.append((i, symbols_init[i], direct_distance, pbc_distance))
                if pbc_distance > 1.0:  # 大于1Å的PBC位移
                    large_pbc_movements.append((i, symbols_init[i], pbc_distance, direct_distance))
        
        # 位移统计
        if distances:
            self.report.append("位移统计 (直接计算):")
            self.report.append(f"  平均位移: {np.mean(distances):.4f} Å")
            self.report.append(f"  最大位移: {np.max(distances):.4f} Å")
            self.report.append(f"  最小位移: {np.min(distances):.4f} Å")
            self.report.append(f"  位移 > 0.1 Å 的原子数: {sum(1 for d in distances if d > 0.1)}")
            self.report.append(f"  位移 > 1.0 Å 的原子数: {sum(1 for d in distances if d > 1.0)}")
            self.report.append("")
            
            self.report.append("位移统计 (PBC感知):")
            self.report.append(f"  平均位移: {np.mean(pbc_distances):.4f} Å")
            self.report.append(f"  最大位移: {np.max(pbc_distances):.4f} Å")
            self.report.append(f"  最小位移: {np.min(pbc_distances):.4f} Å")
            self.report.append(f"  位移 > 0.1 Å 的原子数: {sum(1 for d in pbc_distances if d > 0.1)}")
            self.report.append(f"  位移 > 1.0 Å 的原子数: {sum(1 for d in pbc_distances if d > 1.0)}")
            
            # 计算PBC修正效果
            corrected_atoms = sum(1 for d1, d2 in zip(distances, pbc_distances) if abs(d1 - d2) > 0.1)
            self.report.append(f"  PBC修正的原子数 (差异>0.1Å): {corrected_atoms}")
            self.report.append("")
        
        # 大位移原子详情 (直接计算)
        if large_movements:
            self.report.append("大位移原子 (>1.0 Å, 直接计算):")
            for idx, element, direct_dist, pbc_dist in large_movements[:10]:  # 只显示前10个
                pos_init = positions_init[idx]
                pos_final = positions_final[idx]
                self.report.append(f"  原子 {idx:3d} ({element}): 直接={direct_dist:.4f} Å, PBC={pbc_dist:.4f} Å")
                self.report.append(f"    初态位置: [{pos_init[0]:8.4f}, {pos_init[1]:8.4f}, {pos_init[2]:8.4f}]")
                self.report.append(f"    终态位置: [{pos_final[0]:8.4f}, {pos_final[1]:8.4f}, {pos_final[2]:8.4f}]")
                if abs(direct_dist - pbc_dist) > 0.5:
                    self.report.append(f"    *** PBC效应显著: 距离差异 {abs(direct_dist - pbc_dist):.4f} Å ***")
            self.report.append("")
            
        # 仅在PBC感知计算中的大位移原子
        pbc_only_movements = [mv for mv in large_pbc_movements 
                             if mv[0] not in [x[0] for x in large_movements]]
        if pbc_only_movements:
            self.report.append("仅在PBC感知计算中的大位移原子 (>1.0 Å):")
            for idx, element, pbc_dist, direct_dist in pbc_only_movements[:10]:
                pos_init = positions_init[idx]
                pos_final = positions_final[idx]
                self.report.append(f"  原子 {idx:3d} ({element}): PBC={pbc_dist:.4f} Å, 直接={direct_dist:.4f} Å")
                self.report.append(f"    初态位置: [{pos_init[0]:8.4f}, {pos_init[1]:8.4f}, {pos_init[2]:8.4f}]")
                self.report.append(f"    终态位置: [{pos_final[0]:8.4f}, {pos_final[1]:8.4f}, {pos_final[2]:8.4f}]")
            self.report.append("")
        
        # PBC效应诊断
        if large_movements or large_pbc_movements:
            self.report.append("-"*60)
            self.report.append("PBC效应诊断")
            self.report.append("-"*60)
            
            # 统计PBC修正的显著性
            significant_corrections = [(i, elem, direct, pbc) for i, elem, direct, pbc in large_movements 
                                     if abs(direct - pbc) > 0.5]
            
            if significant_corrections:
                self.report.append(f"检测到 {len(significant_corrections)} 个原子受PBC效应影响显著 (距离差异>0.5Å):")
                for idx, elem, direct, pbc in significant_corrections:
                    reduction = direct - pbc
                    percentage = (reduction / direct) * 100
                    self.report.append(f"  原子 {idx:3d} ({elem}): 距离从 {direct:.4f} Å 降至 {pbc:.4f} Å (-{reduction:.4f} Å, -{percentage:.1f}%)")
                
                self.report.append("")
                self.report.append("PBC效应分析:")
                self.report.append("  - 大的直接距离可能是由于原子'跳跃'到相邻晶胞造成的")
                self.report.append("  - PBC感知计算考虑了最小镜像约定，给出更真实的原子移动距离")
                self.report.append("  - 如果PBC距离显著小于直接距离，说明原子实际移动距离较小")
                self.report.append("")
            else:
                self.report.append("未检测到显著的PBC效应影响")
                self.report.append("")

        self._write_report()  # 实时写入
    
    def check_structure_matching(self):
        """检查结构匹配 (仅Cu原子)
        
        注意: 即使在外部轨迹模式下，此检查也很重要，因为:
        1. 确保轨迹文件中的结构与预期的化学反应一致
        2. 验证Cu基底的排列没有发生意外变化
        3. 保证NEB计算的物理意义正确性
        """
        self._add_section("4. 结构匹配检查")
        
        # 提取Cu原子
        cu_indices = [i for i, symbol in enumerate(self.initial_frame.get_chemical_symbols()) if symbol == 'Cu']
        
        if not cu_indices:
            self.report.append("警告: 未找到Cu原子，跳过结构匹配检查")
            self.report.append("")
            self._write_report()  # 实时写入
            return
        
        initial_cu_only = self.initial_frame[cu_indices]
        final_cu_only = self.final_frame[cu_indices]
        
        self.report.append(f"检查Cu原子结构匹配 (共 {len(cu_indices)} 个Cu原子)...")
        
        if self.trajectory_file:
            self.report.append("注意: 在外部轨迹模式下，此检查验证参考结构的Cu基底一致性")
        
        try:
            adaptor = AseAtomsAdaptor()
            s_ini = adaptor.get_structure(initial_cu_only)
            s_fin = adaptor.get_structure(final_cu_only)
            
            sm = StructureMatcher(primitive_cell=False)
            
            if sm.fit(s_ini, s_fin):
                self.report.append("✓ Cu原子结构匹配成功 (忽略C, H, O原子)")
            else:
                self.report.append("✗ Cu原子结构匹配失败")
                raise ValueError("Cu原子排序或排列不匹配")
                
        except Exception as e:
            self.report.append(f"✗ 结构匹配检查失败: {e}")
            self._write_report()  # 实时写入错误信息
            raise
        
        self.report.append("")
        self._write_report()  # 实时写入
        
        self.report.append("")
    
    def run_neb_calculation(self):
        """执行NEB计算"""
        self._add_section("5. NEB计算")
        
        print("正在执行NEB计算...")
        
        # 初始化计算器
        self.report.append("初始化计算器...")
        self._write_report()  # 实时写入
        predictor = load_predict_unit(self.checkpoint_path)
        self.report.append(f"✓ 计算器初始化完成 (模型: {self.checkpoint_path})")
        self.report.append("")
        self._write_report()  # 实时写入
        
        # 获取插值帧
        if self.trajectory_file and os.path.exists(self.trajectory_file):
            # 使用外部轨迹文件
            self.report.append(f"使用外部轨迹文件: {self.trajectory_file}")
            self._write_report()
            
            frame_set = read(self.trajectory_file, ":")
            if len(frame_set) < 3:
                raise ValueError(f"轨迹文件中帧数不足 (当前: {len(frame_set)}, 至少需要: 3)")
            
            self.num_frames = len(frame_set)
            self.report.append(f"✓ 从轨迹文件加载 {len(frame_set)} 帧")
            
            # 验证轨迹文件首末帧与参考结构的一致性
            self.report.append("")
            self.report.append("验证轨迹文件一致性:")
            initial_loaded = frame_set[0]
            final_loaded = frame_set[-1]
            
            # 检查原子数是否一致
            atoms_match = True
            if len(initial_loaded) != len(self.initial_frame):
                self.report.append(f"⚠️  首帧原子数不匹配: 轨迹({len(initial_loaded)}) vs 参考({len(self.initial_frame)})")
                atoms_match = False
            if len(final_loaded) != len(self.final_frame):
                self.report.append(f"⚠️  末帧原子数不匹配: 轨迹({len(final_loaded)}) vs 参考({len(self.final_frame)})")
                atoms_match = False
            
            # 检查化学符号是否一致
            if initial_loaded.get_chemical_symbols() != self.initial_frame.get_chemical_symbols():
                self.report.append("⚠️  首帧化学符号不匹配")
                atoms_match = False
            if final_loaded.get_chemical_symbols() != self.final_frame.get_chemical_symbols():
                self.report.append("⚠️  末帧化学符号不匹配")
                atoms_match = False
            
            if atoms_match:
                self.report.append("✓ 轨迹文件首末帧与参考结构匹配")
            
        else:
            # 使用内置插值算法
            if self.trajectory_file:
                self.report.append(f"⚠️  指定的轨迹文件不存在: {self.trajectory_file}")
                self.report.append("回退到内置插值算法")
            else:
                self.report.append("使用内置插值算法生成初始路径")
            
            self.report.append(f"生成插值帧 (共 {self.num_frames} 帧)...")
            self._write_report()  # 实时写入
            frame_set = interpolate(self.initial_frame, self.final_frame, self.num_frames)
            self.report.append(f"✓ 使用内置算法生成 {len(frame_set)} 帧")
        
        # 保存插值帧可视化
        interpolated_html = os.path.join(self.output_dir, "interpolated_frames.html")
        x3d_interpolated = X3D(frame_set)
        x3d_interpolated.write(interpolated_html)
        self.report.append(f"✓ 插值帧可视化已保存: {interpolated_html}")
        self.report.append("")
        self._write_report()  # 实时写入
        
        # 设置NEB
        self.report.append("设置NEB计算...")
        self.report.append(f"  弹簧常数 k: {self.k}")
        self.report.append(f"  收敛标准 fmax: {self.fmax} eV/Å")
        self.report.append(f"  爬升镜像阈值: {self.fmax + self.delta_fmax_climb} eV/Å")
        self.report.append(f"  随机种子: {self.random_seed} ({self.seed_source})")
        self._write_report()  # 实时写入
        
        # 使用DyNEB代替OCPNEB
        neb = DyNEB(frame_set, k=self.k)
        
        # 为每个镜像设置计算器
        for image in frame_set:
            image.calc = FAIRChemCalculator(predictor, task_name="oc20")
        
        # 优化
        trajectory_file = os.path.join(self.output_dir, f"{self.reaction_label}.traj")
        optimizer = BFGS(neb, trajectory=trajectory_file)
        
        # 记录收敛信息
        convergence_info = {
            'phase1': {'converged': False, 'steps': 0, 'final_fmax': None},
            'phase2': {'converged': False, 'steps': 0, 'final_fmax': None}
        }
        
        self.report.append("")
        self.report.append("开始优化 (第一阶段 - 无爬升镜像)...")
        self.report.append(f"  目标收敛标准: {self.fmax + self.delta_fmax_climb:.4f} eV/Å")
        self.report.append(f"  最大步数: {self.max_steps_phase1}")
        self._write_report()  # 实时写入
        
        # 获取初始受力信息
        initial_forces = neb.get_forces()
        initial_fmax = np.max(np.abs(initial_forces))
        self.report.append(f"  初始最大受力: {initial_fmax:.4f} eV/Å")
        self._write_report()
        
        conv1 = optimizer.run(fmax=self.fmax + self.delta_fmax_climb, steps=self.max_steps_phase1)
        
        # 记录第一阶段结果
        convergence_info['phase1']['converged'] = conv1
        convergence_info['phase1']['steps'] = optimizer.nsteps
        final_forces_phase1 = neb.get_forces()
        final_fmax_phase1 = np.max(np.abs(final_forces_phase1))
        convergence_info['phase1']['final_fmax'] = final_fmax_phase1
        
        self.report.append("")
        self.report.append("第一阶段优化结果:")
        self.report.append(f"  执行步数: {convergence_info['phase1']['steps']}")
        self.report.append(f"  最终最大受力: {final_fmax_phase1:.4f} eV/Å")
        self.report.append(f"  收敛标准: {self.fmax + self.delta_fmax_climb:.4f} eV/Å")
        
        if conv1:
            self.report.append("  ✓ 第一阶段已收敛")
            self.report.append("")
            self.report.append("开始优化 (第二阶段 - 爬升镜像)...")
            self.report.append(f"  目标收敛标准: {self.fmax:.4f} eV/Å")
            self.report.append(f"  最大步数: {self.max_steps_phase2}")
            self._write_report()  # 实时写入
            
            neb.climb = True
            # 重置优化器计数器
            optimizer.nsteps = 0
            conv2 = optimizer.run(fmax=self.fmax, steps=self.max_steps_phase2)
            
            # 记录第二阶段结果
            convergence_info['phase2']['converged'] = conv2
            convergence_info['phase2']['steps'] = optimizer.nsteps
            final_forces_phase2 = neb.get_forces()
            final_fmax_phase2 = np.max(np.abs(final_forces_phase2))
            convergence_info['phase2']['final_fmax'] = final_fmax_phase2
            
            self.report.append("")
            self.report.append("第二阶段优化结果:")
            self.report.append(f"  执行步数: {convergence_info['phase2']['steps']}")
            self.report.append(f"  最终最大受力: {final_fmax_phase2:.4f} eV/Å")
            self.report.append(f"  收敛标准: {self.fmax:.4f} eV/Å")
            
            if conv2:
                self.report.append("  ✓ 第二阶段已收敛")
                self.report.append("")
                self.report.append("🎉 NEB计算完全收敛!")
            else:
                self.report.append("  ✗ 第二阶段未收敛")
                self.report.append("")
                self.report.append("⚠️  警告: NEB计算部分收敛（第一阶段收敛，第二阶段未收敛）")
                self.report.append("   建议:")
                self.report.append("   - 增加第二阶段的最大步数")
                self.report.append("   - 放宽收敛标准")
                self.report.append("   - 检查反应路径是否合理")
        else:
            self.report.append("  ✗ 第一阶段未收敛")
            self.report.append("")
            self.report.append("❌ NEB计算未收敛!")
            self.report.append("   可能的原因:")
            self.report.append("   - 初态和终态结构差异过大")
            self.report.append("   - 插值路径不合理")
            self.report.append("   - 收敛标准过严格")
            self.report.append("   - 需要更多优化步数")
            self.report.append("")
            self.report.append("   建议:")
            self.report.append("   - 增加第一阶段的最大步数")
            self.report.append("   - 放宽收敛标准")
            self.report.append("   - 检查初态和终态结构")
            self.report.append("   - 使用更好的初始路径猜测")
        
        # 收敛总结
        self.report.append("")
        self.report.append("="*80)
        self.report.append("详细收敛信息总结")
        self.report.append("="*80)
        
        # 计算总的收敛状态
        total_converged = convergence_info['phase1']['converged'] and convergence_info['phase2']['converged']
        partially_converged = convergence_info['phase1']['converged'] and not convergence_info['phase2']['converged']
        not_converged = not convergence_info['phase1']['converged']
        
        # 收敛状态标题
        if total_converged:
            status_emoji = "🎉"
            status_text = "完全收敛"
            status_color = "✓"
        elif partially_converged:
            status_emoji = "⚠️ "
            status_text = "部分收敛"
            status_color = "⚠️"
        else:
            status_emoji = "❌"
            status_text = "未收敛"
            status_color = "✗"
        
        self.report.append(f"{status_emoji} 总体收敛状态: {status_color} {status_text}")
        self.report.append("")
        
        # 详细的阶段分析
        self.report.append("阶段收敛详情:")
        self.report.append("-" * 40)
        
        # 第一阶段
        phase1_status = "✓ 收敛" if convergence_info['phase1']['converged'] else "✗ 未收敛"
        self.report.append(f"第一阶段 (预优化): {phase1_status}")
        self.report.append(f"  执行步数: {convergence_info['phase1']['steps']}")
        self.report.append(f"  初始最大受力: {initial_fmax:.4f} eV/Å")
        self.report.append(f"  最终最大受力: {final_fmax_phase1:.4f} eV/Å")
        self.report.append(f"  收敛标准: {self.fmax + self.delta_fmax_climb:.4f} eV/Å")
        
        # 第二阶段（如果第一阶段收敛）
        if convergence_info['phase1']['converged']:
            phase2_status = "✓ 收敛" if convergence_info['phase2']['converged'] else "✗ 未收敛"
            self.report.append(f"第二阶段 (爬升镜像): {phase2_status}")
            self.report.append(f"  执行步数: {convergence_info['phase2']['steps']}")
            self.report.append(f"  初始最大受力: {final_fmax_phase1:.4f} eV/Å")
            self.report.append(f"  最终最大受力: {final_fmax_phase2:.4f} eV/Å")
            self.report.append(f"  收敛标准: {self.fmax:.4f} eV/Å")
        else:
            self.report.append("第二阶段 (爬升镜像): 未执行 (第一阶段未收敛)")
        
        self.report.append("")
        
        # 优化统计信息
        self.report.append("优化统计:")
        self.report.append("-" * 40)
        total_steps = convergence_info['phase1']['steps'] + convergence_info['phase2']['steps']
        self.report.append(f"总优化步数: {total_steps}")
        self.report.append(f"第一阶段步数: {convergence_info['phase1']['steps']} (目标: ≤{self.max_steps_phase1})")
        if convergence_info['phase1']['converged']:
            self.report.append(f"第二阶段步数: {convergence_info['phase2']['steps']} (目标: ≤{self.max_steps_phase2})")
        
        # 受力变化详细统计
        self.report.append("")
        self.report.append("受力变化统计:")
        self.report.append("-" * 40)
        
        force_reduction_phase1 = initial_fmax - final_fmax_phase1
        force_reduction_percent_phase1 = (force_reduction_phase1 / initial_fmax) * 100 if initial_fmax > 0 else 0
        
        self.report.append(f"第一阶段受力减少:")
        self.report.append(f"  绝对减少: {force_reduction_phase1:.4f} eV/Å")
        self.report.append(f"  相对减少: {force_reduction_percent_phase1:.1f}%")
        
        if convergence_info['phase1']['converged']:
            force_reduction_phase2 = final_fmax_phase1 - final_fmax_phase2
            force_reduction_percent_phase2 = (force_reduction_phase2 / final_fmax_phase1) * 100 if final_fmax_phase1 > 0 else 0
            
            self.report.append(f"第二阶段受力减少:")
            self.report.append(f"  绝对减少: {force_reduction_phase2:.4f} eV/Å")
            self.report.append(f"  相对减少: {force_reduction_percent_phase2:.1f}%")
            
            total_force_reduction = initial_fmax - final_fmax_phase2
            total_force_reduction_percent = (total_force_reduction / initial_fmax) * 100 if initial_fmax > 0 else 0
            
            self.report.append(f"总体受力减少:")
            self.report.append(f"  绝对减少: {total_force_reduction:.4f} eV/Å")
            self.report.append(f"  相对减少: {total_force_reduction_percent:.1f}%")
        
        # 收敛质量评估
        self.report.append("")
        self.report.append("收敛质量评估:")
        self.report.append("-" * 40)
        
        if total_converged:
            self.report.append("✓ 优秀: 计算完全收敛，结果可靠")
            print("🎉 NEB计算完全收敛! 结果质量: 优秀")
        elif partially_converged:
            self.report.append("⚠️  一般: 仅第一阶段收敛，结果可参考但建议进一步优化")
            if final_fmax_phase1 < self.fmax * 2:
                self.report.append("   提示: 第一阶段受力已显著降低，结果具有一定参考价值")
            else:
                self.report.append("   警告: 第一阶段受力仍较高，建议调整参数重新计算")
            print("⚠️  NEB计算部分收敛，结果质量: 一般")
        else:
            self.report.append("✗ 差: 计算未收敛，结果不可靠")
            if convergence_info['phase1']['steps'] >= self.max_steps_phase1:
                self.report.append("   建议: 增加第一阶段最大步数或放宽收敛标准")
            if initial_fmax > 5.0:
                self.report.append("   警告: 初始受力过高，可能需要检查结构或插值质量")
            print("❌ NEB计算未收敛，结果质量: 差")
        
        # 推荐后续操作
        self.report.append("")
        self.report.append("推荐后续操作:")
        self.report.append("-" * 40)
        
        if total_converged:
            self.report.append("✓ 可以进行后续分析:")
            self.report.append("  - 计算反应活化能和反应能")
            self.report.append("  - 分析过渡态结构")
            self.report.append("  - 进行频率分析验证")
        elif partially_converged:
            self.report.append("⚠️  建议进一步优化:")
            self.report.append(f"  - 增加第二阶段最大步数 (当前: {self.max_steps_phase2})")
            self.report.append("  - 适当放宽收敛标准")
            self.report.append("  - 检查是否需要更精细的插值")
        else:
            self.report.append("❌ 必须重新计算:")
            self.report.append("  - 检查初态和终态结构合理性")
            self.report.append(f"  - 增加第一阶段最大步数 (当前: {self.max_steps_phase1})")
            self.report.append("  - 考虑放宽收敛标准")
            self.report.append("  - 检查插值路径质量")
        
        self.report.append("")
        self.report.append("="*80)
        self.report.append(f"✓ NEB轨迹已保存: {trajectory_file}")
        self.report.append("="*80)
        self.report.append("")
        self._write_report()  # 实时写入
        
        return trajectory_file, convergence_info
    
    def analyze_energy_profile(self, trajectory_file, convergence_info=None):
        """分析能量剖面"""
        self._add_section("6. 能量剖面分析")
        
        print("正在分析能量剖面...")
        
        # 读取优化后的结构
        optimized_neb = read(trajectory_file, ":")[-self.num_frames:]
        
        # 计算能量
        calc = self._create_calculator()
        energies = []
        
        for i, frame in enumerate(optimized_neb):
            frame.calc = calc
            energy = frame.get_potential_energy()
            energies.append(energy)
            self.report.append(f"  帧 {i}: {energy:.6f} eV")
        
        # 相对能量
        relative_energies = [e - energies[0] for e in energies]
        
        self.report.append("")
        self.report.append("相对能量 (相对于初态):")
        for i, rel_e in enumerate(relative_energies):
            self.report.append(f"  帧 {i}: {rel_e:+.6f} eV")
        
        # 能量统计
        max_energy = max(relative_energies)
        final_energy = relative_energies[-1]
        max_energy_frame = relative_energies.index(max_energy)
        
        self.report.append("")
        self.report.append("能量统计:")
        self.report.append(f"  活化能 (Ea): {max_energy:.4f} eV")
        self.report.append(f"  反应能 (ΔE): {final_energy:+.4f} eV")
        self.report.append(f"  最大能量帧: {max_energy_frame}")
        
        # 能量剖面质量评估（基于收敛状态）
        self.report.append("")
        self.report.append("能量剖面可靠性评估:")
        self.report.append("-" * 40)
        
        # 使用收敛信息来评估可靠性
        if convergence_info:
            total_converged = convergence_info['phase1']['converged'] and convergence_info['phase2']['converged']
            partially_converged = convergence_info['phase1']['converged'] and not convergence_info['phase2']['converged']
            
            if total_converged:
                convergence_quality = "完全收敛"
                base_reliability = "高"
            elif partially_converged:
                convergence_quality = "部分收敛"
                base_reliability = "中等"
            else:
                convergence_quality = "未收敛"
                base_reliability = "低"
            
            self.report.append(f"  NEB收敛状态: {convergence_quality}")
        else:
            base_reliability = "中等"  # 默认值
            self.report.append("  NEB收敛状态: 未知 (无收敛信息)")
        
        # 检查能量是否合理单调或有明确的过渡态
        energy_changes = [relative_energies[i+1] - relative_energies[i] for i in range(len(relative_energies)-1)]
        smooth_profile = all(abs(change) < 2.0 for change in energy_changes)  # 相邻点能量变化不超过2eV
        
        # 综合评估可靠性
        if convergence_info and convergence_info['phase1']['converged'] and convergence_info['phase2']['converged']:
            if smooth_profile and max_energy < 5.0:
                self.report.append("  ✓ 高可靠性: NEB完全收敛，能量剖面平滑合理")
                reliability = "高"
            elif max_energy < 5.0:
                self.report.append("  ✓ 高可靠性: NEB完全收敛，活化能合理")
                reliability = "高"
            else:
                self.report.append("  ⚠️  中等可靠性: NEB收敛但活化能异常高")
                reliability = "中等"
        elif convergence_info and convergence_info['phase1']['converged']:
            if smooth_profile and max_energy < 3.0:
                self.report.append("  ⚠️  中等可靠性: NEB部分收敛，能量剖面相对合理")
                reliability = "中等"
            else:
                self.report.append("  ⚠️  中等可靠性: NEB部分收敛，建议谨慎解释结果")
                reliability = "中等"
        else:
            self.report.append("  ✗ 低可靠性: NEB未收敛，结果仅供参考")
            reliability = "低"
        
        self.report.append(f"  推荐置信度: {reliability}")
        
        # 添加具体建议
        if convergence_info:
            if not convergence_info['phase1']['converged']:
                self.report.append("  ⚠️  建议: 由于第一阶段未收敛，能量值可能不准确")
            elif not convergence_info['phase2']['converged']:
                self.report.append("  ⚠️  建议: 由于第二阶段未收敛，过渡态能量可能不够精确")
            else:
                self.report.append("  ✓ 建议: 能量值可靠，可用于后续分析")
        
        self.report.append("")
        
        # 过渡态分析
        self.report.append("过渡态分析:")
        self.report.append("-" * 40)
        self.report.append(f"  过渡态位置: 帧 {max_energy_frame}")
        
        if max_energy_frame == 0:
            self.report.append("  ⚠️  警告: 过渡态在初态，可能插值有问题")
        elif max_energy_frame == len(relative_energies) - 1:
            self.report.append("  ⚠️  警告: 过渡态在终态，可能插值有问题")
        elif max_energy_frame <= 2 or max_energy_frame >= len(relative_energies) - 3:
            self.report.append("  ⚠️  注意: 过渡态接近端点，建议增加插值帧数")
        else:
            self.report.append("  ✓ 过渡态位置合理")
        
        # 反应类型分析
        self.report.append("")
        self.report.append("反应特征分析:")
        self.report.append("-" * 40)
        
        if final_energy < -0.5:
            reaction_type = "强放热反应"
            stability = "热力学有利"
        elif final_energy > 0.5:
            reaction_type = "吸热反应"
            stability = "热力学不利"
        else:
            reaction_type = "热力学接近平衡"
            stability = "能量变化较小"
        
        self.report.append(f"  反应类型: {reaction_type}")
        self.report.append(f"  热力学稳定性: {stability}")
        
        if max_energy < 1.0:
            kinetic_barrier = "动力学障碍较低"
        elif max_energy < 2.0:
            kinetic_barrier = "动力学障碍适中"
        elif max_energy < 3.0:
            kinetic_barrier = "动力学障碍较高"
        else:
            kinetic_barrier = "动力学障碍很高"
        
        self.report.append(f"  动力学障碍: {kinetic_barrier}")
        
        self.report.append("")
        
        # 检查活化能是否异常高
        if max_energy > 5.0:
            self.report.append("")
            self.report.append("⚠️  警告: 活化能异常高!")
            self.report.append(f"   活化能 {max_energy:.2f} eV > 5.0 eV")
            self.report.append("   可能的原因:")
            self.report.append("   - 初态和终态结构不匹配")
            self.report.append("   - 插值路径不合理")
            self.report.append("   - NEB计算未收敛")
            self.report.append("   - 选择的反应路径不是最优的")
            self.report.append("   建议检查结构和重新计算")
            print(f"⚠️  警告: 活化能异常高 ({max_energy:.2f} eV > 5.0 eV)，请检查计算结果!")
        elif max_energy > 3.0:
            self.report.append("")
            self.report.append("ℹ️  提示: 活化能较高")
            self.report.append(f"   活化能 {max_energy:.2f} eV > 3.0 eV")
            self.report.append("   建议检查反应路径是否合理")
            print(f"ℹ️  提示: 活化能较高 ({max_energy:.2f} eV)，建议检查反应路径")
        
        # 绘制能量图
        plt.figure(figsize=(10, 6))
        plt.plot(range(len(relative_energies)), relative_energies, 'o-', linewidth=2, markersize=8)
        plt.xlabel("Reaction Coordinate (Frame Index)", fontsize=12)
        plt.ylabel("Relative Energy (eV)", fontsize=12)
        plt.title(f"Reaction Pathway Energy Profile\nActivation Energy Ea = {max_energy:.2f} eV", fontsize=14)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        energy_plot = os.path.join(self.output_dir, "reaction_coordinate.png")
        plt.savefig(energy_plot, dpi=300)
        plt.close()
        
        self.report.append(f"✓ 能量图已保存: {energy_plot}")
        self.report.append("")
        self._write_report()  # 实时写入
        
        return optimized_neb
    
    def generate_visualization(self, optimized_neb):
        """生成可视化文件"""
        self._add_section("7. 可视化文件生成")
        
        print("正在生成可视化文件...")
        
        # 生成NEB可视化
        neb_html = os.path.join(self.output_dir, f"{self.reaction_label}_neb.html")
        x3d = X3D(optimized_neb)
        x3d.write(neb_html)
        
        self.report.append(f"✓ NEB可视化已保存: {neb_html}")
        self.report.append("")
        self._write_report()  # 实时写入
    
    def save_report(self, convergence_info=None):
        """保存分析报告的最终总结"""
        self._add_section("8. 输出文件总结")
        
        output_files = [
            ("分析报告", self.report_file),
            ("插值帧可视化", f"{self.output_dir}/interpolated_frames.html"),
            ("NEB轨迹文件", f"{self.output_dir}/{self.reaction_label}.traj"),
            ("反应能量图", f"{self.output_dir}/reaction_coordinate.png"),
            ("NEB结果可视化", f"{self.output_dir}/{self.reaction_label}_neb.html"),
        ]
        
        self.report.append("生成的输出文件:")
        for desc, filepath in output_files:
            if os.path.exists(filepath):
                self.report.append(f"  ✓ {desc}: {filepath}")
            else:
                self.report.append(f"  ✗ {desc}: {filepath} (未找到)")
        
        # 添加最终收敛状态总结
        if convergence_info:
            self.report.append("")
            self.report.append("="*80)
            self.report.append("最终计算状态总结")
            self.report.append("="*80)
            
            total_converged = convergence_info['phase1']['converged'] and convergence_info['phase2']['converged']
            partially_converged = convergence_info['phase1']['converged'] and not convergence_info['phase2']['converged']
            
            if total_converged:
                self.report.append("🎉 计算状态: 完全成功")
                self.report.append("✓ NEB优化: 两个阶段均已收敛")
                self.report.append("✓ 结果质量: 优秀，可信度高")
                self.report.append("✓ 推荐: 结果可直接用于发表或进一步分析")
            elif partially_converged:
                self.report.append("⚠️  计算状态: 部分成功")
                self.report.append("✓ NEB优化: 第一阶段收敛，第二阶段未收敛")
                self.report.append("⚠️  结果质量: 良好，但建议进一步优化")
                self.report.append("⚠️  推荐: 可用于初步分析，但建议重新计算第二阶段")
            else:
                self.report.append("❌ 计算状态: 需要改进")
                self.report.append("✗ NEB优化: 第一阶段未收敛")
                self.report.append("✗ 结果质量: 不可靠")
                self.report.append("✗ 推荐: 必须调整参数重新计算")
            
            # 计算效率统计
            total_steps = convergence_info['phase1']['steps'] + convergence_info['phase2']['steps']
            self.report.append("")
            self.report.append("计算效率统计:")
            self.report.append(f"  总优化步数: {total_steps}")
            self.report.append(f"  第一阶段效率: {convergence_info['phase1']['steps']}/{self.max_steps_phase1} 步")
            if convergence_info['phase1']['converged']:
                self.report.append(f"  第二阶段效率: {convergence_info['phase2']['steps']}/{self.max_steps_phase2} 步")
            
            efficiency = "高" if total_steps < 300 else ("中等" if total_steps < 400 else "低")
            self.report.append(f"  计算效率: {efficiency}")
        
        self.report.append("")
        self.report.append("="*80)
        self.report.append("NEB计算和分析完成")
        self.report.append("="*80)
        
        # 最终写入完整报告
        self._write_report()
        
        print(f"\n分析报告已保存到: {self.report_file}")
    
    def run_full_analysis(self):
        """运行完整的NEB分析流程"""
        try:
            print("开始NEB计算和分析...")
            
            # 1. 加载结构
            self.load_structures()
            
            # 2. 分析PBC
            self.analyze_pbc()
            
            # 3. 分析原子位置变化
            self.analyze_atomic_positions()
            
            # 4. 检查结构匹配
            self.check_structure_matching()
            
            # 5. 执行NEB计算
            trajectory_file, convergence_info = self.run_neb_calculation()
            
            # 6. 分析能量剖面
            optimized_neb = self.analyze_energy_profile(trajectory_file, convergence_info)
            
            # 7. 生成可视化
            self.generate_visualization(optimized_neb)
            
            # 8. 保存报告
            self.save_report(convergence_info)
            
            print("NEB分析完成！")
            
        except Exception as e:
            self.report.append("")
            self.report.append("="*80)
            self.report.append("错误信息")
            self.report.append("="*80)
            self.report.append(f"计算过程中出现错误: {e}")
            self._write_report()  # 实时写入错误信息
            print(f"错误: {e}")
            raise
    
    def _calculate_pbc_distance(self, pos1, pos2, cell):
        """
        计算考虑周期边界条件的最小距离
        
        Args:
            pos1: 第一个原子的位置 (Å)
            pos2: 第二个原子的位置 (Å)
            cell: 晶胞矩阵 (3x3 array)
            
        Returns:
            最小距离 (Å) 和调整后的位置差向量
        """
        # 计算位置差
        diff = pos2 - pos1
        
        # 转换到分数坐标
        if np.linalg.det(cell) > 1e-10:  # 确保晶胞矩阵不是奇异的
            inv_cell = np.linalg.inv(cell)
            frac_diff = np.dot(diff, inv_cell.T)
            
            # 应用最小镜像约定 (minimum image convention)
            frac_diff = frac_diff - np.round(frac_diff)
            
            # 转换回笛卡尔坐标
            cart_diff = np.dot(frac_diff, cell)
            
            # 计算距离
            distance = np.linalg.norm(cart_diff)
            return distance, cart_diff
        else:
            # 如果晶胞矩阵有问题，返回直接距离
            distance = np.linalg.norm(diff)
            return distance, diff
    
    def _unwrap_positions(self, positions, cell, reference_positions=None):
        """
        展开原子位置，消除PBC跳跃
        
        Args:
            positions: 原子位置数组
            cell: 晶胞矩阵
            reference_positions: 参考位置（用于确定展开方向）
            
        Returns:
            展开后的位置数组
        """
        if reference_positions is None:
            return positions
            
        unwrapped_positions = positions.copy()
        
        if np.linalg.det(cell) > 1e-10:
            inv_cell = np.linalg.inv(cell)
            
            for i in range(len(positions)):
                # 计算位置差
                diff = positions[i] - reference_positions[i]
                
                # 转换到分数坐标
                frac_diff = np.dot(diff, inv_cell.T)
                
                # 找到最接近的镜像
                frac_shift = np.round(frac_diff)
                cart_shift = np.dot(frac_shift, cell)
                
                # 调整位置
                unwrapped_positions[i] = positions[i] - cart_shift
                
        return unwrapped_positions


def main():
    """
    Main function to run the NEB analysis workflow
    """
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(description="NEB (Nudged Elastic Band) 计算脚本")
    parser.add_argument("reaction_label", nargs="?", default="r11", 
                       help="反应标签 (默认: r11). 脚本会自动查找 data/{reaction_label}/ini.vasp 和 data/{reaction_label}/fin.vasp")
    parser.add_argument("--final-file", 
                       help="指定终态文件路径 (默认: data/{reaction_label}/fin.vasp)")
    parser.add_argument("--trajectory-file", "--traj", 
                       help="指定外部轨迹文件路径. 如果提供，将使用该文件中的帧而非内置插值算法")
    
    # NEB计算参数
    parser.add_argument("--fmax", type=float, default=0.05,
                       help="力收敛标准 (eV/Å, 默认: 0.05)")
    parser.add_argument("--delta-fmax-climb", type=float, default=0.4,
                       help="攀爬图像的力阈值 (eV/Å, 默认: 0.4)")
    parser.add_argument("--spring-constant", "--k", type=float, default=1.0,
                       help="弹簧常数 (默认: 1.0)")
    parser.add_argument("--num-frames", type=int, default=10,
                       help="插值帧数 (默认: 10)")
    parser.add_argument("--cpu", action="store_true", default=False,
                       help="使用CPU而非GPU进行计算")
    parser.add_argument("--checkpoint-path", default="uma_sm.pt",
                       help="模型检查点文件路径 (默认: uma_sm.pt)")
    parser.add_argument("--max-steps-phase1", type=int, default=200,
                       help="第一阶段优化最大步数 (默认: 200)")
    parser.add_argument("--max-steps-phase2", type=int, default=300,
                       help="第二阶段优化最大步数 (默认: 300)")
    parser.add_argument("--random-seed", type=int, default=None,
                       help="随机种子，用于确保结果可重现 (默认: 随机生成)")
    
    args = parser.parse_args()
    reaction_label = args.reaction_label
    
    try:
        # 设置文件路径
        output_dir = f'data/{reaction_label}'
        initial_file = f'data/{reaction_label}/ini.vasp'
        
        # 检查是否指定了自定义终态文件
        if args.final_file:
            final_file = args.final_file
        else:
            # 使用 fin.vasp
            changed_final_file = f'data/{reaction_label}/fin.vasp'
            default_final_file = f'data/{reaction_label}/fin.vasp'
            
            if os.path.exists(changed_final_file):
                final_file = changed_final_file
                print(f"使用修改后的终态文件: {changed_final_file}")
            elif os.path.exists(default_final_file):
                final_file = default_final_file
                print(f"使用默认终态文件: {default_final_file}")
            else:
                raise FileNotFoundError(f"未找到终态文件: {changed_final_file} 或 {default_final_file}")
        
        # 检查文件是否存在
        if not os.path.exists(initial_file):
            raise FileNotFoundError(f"初态文件不存在: {initial_file}")
        if not os.path.exists(final_file):
            raise FileNotFoundError(f"终态文件不存在: {final_file}")
        
        print(f"开始分析反应: {reaction_label}")
        print(f"初态文件: {initial_file}")
        print(f"终态文件: {final_file}")
        print(f"输出目录: {output_dir}")
        if args.trajectory_file:
            print(f"外部轨迹文件: {args.trajectory_file}")
        
        # 打印NEB参数
        print(f"NEB参数:")
        print(f"  力收敛标准 (fmax): {args.fmax} eV/Å")
        print(f"  攀爬图像力阈值: {args.delta_fmax_climb} eV/Å")
        print(f"  弹簧常数: {args.spring_constant}")
        print(f"  插值帧数: {args.num_frames}")
        print(f"  计算设备: {'CPU' if args.cpu else 'GPU'}")
        print(f"  模型检查点: {args.checkpoint_path}")
        print(f"  第一阶段最大步数: {args.max_steps_phase1}")
        print(f"  第二阶段最大步数: {args.max_steps_phase2}")
        if args.random_seed is not None:
            print(f"  随机种子: {args.random_seed} (用户指定)")
        else:
            print(f"  随机种子: 将自动生成")
        print("-" * 50)
        
        # 初始化分析器
        analyzer = NEBAnalyzer(
            initial_file, final_file, output_dir, reaction_label, 
            trajectory_file=args.trajectory_file,
            fmax=args.fmax,
            delta_fmax_climb=args.delta_fmax_climb,
            k=args.spring_constant,
            num_frames=args.num_frames,
            cpu=args.cpu,
            checkpoint_path=args.checkpoint_path,
            max_steps_phase1=args.max_steps_phase1,
            max_steps_phase2=args.max_steps_phase2,
            random_seed=args.random_seed
        )
        
        # 运行完整的分析工作流程
        analyzer.run_full_analysis()
        
        # 写入完成信息
        analyzer._write_report("\n" + "="*80)
        analyzer._write_report("ANALYSIS COMPLETED SUCCESSFULLY")
        analyzer._write_report("="*80)
        
        print(f"\n✓ 分析完成!")
        print(f"结果保存到: {output_dir}/")
        print(f"详细报告: neb_analysis_report.txt")
        
    except FileNotFoundError as e:
        error_msg = f"文件错误: {str(e)}"
        print(error_msg)
        print("\n使用说明:")
        print(f"python neb.py [reaction_label]")
        print(f"")
        print(f"示例:")
        print(f"python neb.py r11     # 分析 data/r11/ini.vasp 和 data/r11/fin.vasp")
        print(f"python neb.py r25     # 分析 data/r25/ini.vasp 和 data/r25/fin.vasp")
        print(f"")
        print(f"确保存在以下文件:")
        print(f"- data/{reaction_label}/ini.vasp")
        print(f"- data/{reaction_label}/fin.vasp 或 data/{reaction_label}/changed_fin.vasp")
        
        sys.exit(1)
        
    except Exception as e:
        error_msg = f"执行错误: {str(e)}"
        print(error_msg)
        
        # 尝试写入错误到报告文件
        try:
            with open('neb_analysis_report.txt', 'a') as f:
                f.write(f"\n\nERROR: {error_msg}\n")
        except:
            pass
        
        raise


if __name__ == "__main__":
    main()
