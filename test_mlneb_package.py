#!/usr/bin/env python3
"""
测试ML-NEB包的基本功能
"""

import os
import sys

def test_imports():
    """测试包导入"""
    print("测试包导入...")
    try:
        import mlneb
        print(f"✓ 成功导入mlneb包，版本: {mlneb.__version__}")
        
        from mlneb import NEBAnalyzer
        print("✓ 成功导入NEBAnalyzer")
        
        from mlneb import combine_vasp_to_trajectory
        print("✓ 成功导入combine_vasp_to_trajectory")
        
        from mlneb import convert_trajectory_to_html
        print("✓ 成功导入convert_trajectory_to_html")
        
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_trajectory_functions():
    """测试轨迹处理功能"""
    print("\n测试轨迹处理功能...")
    try:
        from mlneb.utils.trajectory import analyze_trajectory
        
        # 检查是否有现有的轨迹文件
        test_files = ["combined_trajectory.traj", "data/r11/r11.traj"]
        
        for traj_file in test_files:
            if os.path.exists(traj_file):
                print(f"分析轨迹文件: {traj_file}")
                info = analyze_trajectory(traj_file)
                print(f"  ✓ 帧数: {info['frame_count']}")
                print(f"  ✓ 原子数: {info['atom_count']}")
                print(f"  ✓ 元素: {info['element_count']}")
                return True
        
        print("⚠️  未找到测试轨迹文件，跳过轨迹分析测试")
        return True
        
    except Exception as e:
        print(f"✗ 轨迹功能测试失败: {e}")
        return False

def test_cli_modules():
    """测试CLI模块"""
    print("\n测试CLI模块...")
    try:
        from mlneb.cli import neb_cli, traj_cli, combine_cli
        print("✓ 成功导入CLI模块")
        
        # 测试解析器创建
        neb_parser = neb_cli.create_neb_parser()
        traj_parser = traj_cli.create_traj_parser()
        combine_parser = combine_cli.create_combine_parser()
        print("✓ 成功创建命令行解析器")
        
        return True
    except Exception as e:
        print(f"✗ CLI模块测试失败: {e}")
        return False

def test_package_structure():
    """测试包结构"""
    print("\n测试包结构...")
    
    expected_modules = [
        "mlneb.core",
        "mlneb.core.analyzer",
        "mlneb.utils",
        "mlneb.utils.trajectory",
        "mlneb.visualization",
        "mlneb.visualization.html",
        "mlneb.cli.neb_cli",
        "mlneb.scripts"
    ]
    
    all_good = True
    for module in expected_modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError as e:
            print(f"✗ {module}: {e}")
            all_good = False
    
    return all_good

def main():
    """主测试函数"""
    print("="*60)
    print("ML-NEB包功能测试")
    print("="*60)
    
    tests = [
        ("包导入", test_imports),
        ("包结构", test_package_structure),
        ("CLI模块", test_cli_modules),
        ("轨迹功能", test_trajectory_functions),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(tests)} 个测试通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！ML-NEB包已正确安装和配置。")
        return True
    else:
        print("⚠️  部分测试失败，请检查包的安装和配置。")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
