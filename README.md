# NEB Analysis Tool

A Nudged Elastic Band (NEB) calculation script based on FAIRChem for analyzing chemical reaction pathways and transition states.

## Overview

This tool automates NEB calculations to determine minimum energy pathways between initial and final states of chemical reactions. It provides comprehensive analysis capabilities including convergence monitoring, energy profile generation, and detailed reporting with visualization outputs.

## Key Features

- **Automated NEB Calculations**: Performs two-phase optimization with configurable parameters
- **Comprehensive Analysis**: Generates detailed reports with convergence metrics and energy profiles
- **Flexible Input Options**: Supports both built-in interpolation and external trajectory files
- **Configurable Parameters**: Extensive command-line options for fine-tuning calculations
- **Structure Analysis**: Automatic structure matching and atomic displacement tracking
- **Visualization**: Interactive HTML outputs and reaction coordinate plots
- **Reproducible Results**: Random seed control for consistent calculations

## Installation

### Prerequisites
- CUDA-compatible GPU (recommended)
- `uv` package manager

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mlneb
   ```

2. **Install dependencies**
   ```bash
   uv sync
   ```

3. **Activate the virtual environment**
   ```bash
   source .venv/bin/activate
   ```

4. **Download model checkpoint**
   Ensure the `uma_sm.pt` model file is available in the working directory.

## Usage

### Basic Commands

```bash
# Run NEB calculation for reaction r11 with default parameters
python neb.py r11

# Use external trajectory file
python neb.py r11 --trajectory-file combined_trajectory.traj

# Custom parameters for high-precision calculation
python neb.py r25 --fmax 0.02 --max-steps-phase1 300 --random-seed 123
```

### Command-Line Parameters

| Parameter | Description | Default | Units |
|-----------|-------------|---------|-------|
| `reaction_label` | Reaction identifier | r11 | - |
| `--fmax` | Force convergence criterion | 0.05 | eV/Å |
| `--delta-fmax-climb` | Climbing image force threshold | 0.4 | eV/Å |
| `--spring-constant` / `-k` | NEB spring constant | 1.0 | - |
| `--num-frames` | Number of interpolated frames | 10 | - |
| `--max-steps-phase1` | Phase 1 optimization steps | 200 | - |
| `--max-steps-phase2` | Phase 2 optimization steps | 300 | - |
| `--random-seed` | Random seed for reproducibility | auto-generated | - |
| `--cpu` | Force CPU usage (disable GPU) | False | - |
| `--checkpoint-path` | Model checkpoint file path | uma_sm.pt | - |
| `--trajectory-file` | External trajectory input | None | - |

## Input Requirements

The script expects the following directory structure:

```
data/{reaction_label}/
├── ini.vasp          # Initial state structure
└── fin.vasp          # Final state structure
```

Both files should be in VASP POSCAR format containing the atomic structures.

## Output Files

All outputs are generated in the `data/{reaction_label}/` directory:

| File | Description |
|------|-------------|
| `neb_analysis_report_{reaction_label}.txt` | Comprehensive analysis report |
| `interpolated_frames.html` | Interactive visualization of interpolated frames |
| `{reaction_label}.traj` | NEB trajectory file (ASE format) |
| `reaction_coordinate.png` | Reaction coordinate energy plot |
| `{reaction_label}_neb.html` | Interactive visualization of optimized NEB path |

## Advanced Usage Examples

### High-Precision Calculations
```bash
# Tight convergence for publication-quality results
python neb.py r11 --fmax 0.01 --max-steps-phase1 500 --max-steps-phase2 500
```

### Reproducible Research
```bash
# Fixed seed for reproducible results
python neb.py r25 --random-seed 42
```

### Resource-Constrained Environments
```bash
# CPU-only calculation
python neb.py r11 --cpu

# Quick test with fewer frames
python neb.py r11 --num-frames 5 --max-steps-phase1 50
```

### Custom Spring Constants
```bash
# Stiffer springs for loose initial guess
python neb.py r11 --spring-constant 2.0

# Looser springs for well-defined pathway
python neb.py r11 -k 0.5
```

## Best Practices

### Convergence Settings
- **Standard calculations**: Use default `--fmax 0.05`
- **High-precision work**: Use `--fmax 0.01` or tighter
- **Initial testing**: Use `--fmax 0.1` for faster convergence

### Computational Resources
- **GPU recommended**: Significantly faster than CPU-only calculations
- **Memory considerations**: Larger `--num-frames` requires more memory
- **Time management**: Increase `--max-steps-phase2` for difficult convergence

### Reproducibility
- Always specify `--random-seed` for publishable results
- Document all parameters used in your analysis
- Save the analysis report for parameter tracking

## Troubleshooting

### Common Issues
1. **Convergence problems**: Increase `--max-steps-phase2` or relax `--fmax`
2. **Memory errors**: Reduce `--num-frames` or use `--cpu`
3. **Structure mismatch**: Verify `ini.vasp` and `fin.vasp` have identical atomic ordering

### Performance Optimization
- Use GPU when available (default behavior)
- Adjust `--num-frames` based on system complexity
- Monitor convergence in analysis reports

## Technical Details

The calculation proceeds in two phases:
1. **Phase 1**: Standard NEB optimization to establish the pathway
2. **Phase 2**: Climbing-image NEB to locate the transition state accurately

Random seeds are automatically generated using high-resolution timestamps when not specified, ensuring reproducibility when needed while avoiding identical runs by default.