<html>
<head>
<title>ASE atomic visualization</title>
<link rel="stylesheet" type="text/css"
 href="https://www.x3dom.org/x3dom/release/x3dom.css">
</link>
<script type="text/javascript"
 src="https://www.x3dom.org/x3dom/release/x3dom.js">
</script>
<style>
* {
    box-sizing: border-box;
  }
 
/* Create two unequal columns that floats next to each other */
.column {
  float: left;
  padding: 1px;
}

.left {
  width: 20%;
}

.right {
  width: 80%;
}

/* Clear floats after the columns */
.row:after {
  content: "";
  display: table;
  clear: both;
}

#x3dase{
    top:0;
    width: 80%;
    height: 80%;
    border:2px solid darkorange;        
}

#sidebar{
    top:0;
    border:2px solid darkorange;        
}


/* Sidebar component containers */
.ui-widget-header
{
  background-color: lightblue;
  font-size: 12px;

}
.sidebarComponent
{
    padding:2px 2px 2px 2px;
    font-size: medium;
}

.button {
  background-color: #4CAF50; /* Green */
  border: 1px solid green;
  color: white;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 10px;
  cursor: pointer;
  /* float: left; */
}

</style></head>
<body>
<div class = "column left", id = "sidebar">

    <div class="ui-widget-header">Model</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="spacefilling('3c2f468c')">Ball</button>
            <button type="button" class = "button" onclick="ballstick('3c2f468c')">Ball-and-stick</button>
            <button type="button" class = "button" onclick="polyhedra('3c2f468c')">Polyhedra</button>
        </form>
    </div>
    <div class="ui-widget-header">Label</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="none('3c2f468c')">None</button>
            <button type="button" class = "button" onclick="element('3c2f468c')"> Element</button>
            <button type="button" class = "button" onclick="index('3c2f468c')">Index</button>
        </form>
    </div>

    <div class="ui-widget-header">Camera</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="document.getElementById('camera_ortho_3c2f468c').setAttribute('set_bind','true');">Orthographic</button>
            <button type="button" class = "button" onclick="document.getElementById('camera_persp_3c2f468c').setAttribute('set_bind','true');">Perspective</button>
        </form>
    </div>

    <div class="ui-widget-header">View</div>
    <div class="sidebarComponent">
        <form style="text-align:left;">
            <button type="button" class = "button" onclick="set_viewpoint('3c2f468c', 'top_pos', 'top_ori')">Top</button>
            <button type="button" class = "button" onclick="set_viewpoint('3c2f468c', 'front_pos', 'front_ori')">Front</button>
            <button type="button" class = "button" onclick="set_viewpoint('3c2f468c', 'right_pos', 'right_ori')">Right</button>
        </form>
    </div>

    <div class="ui-widget-header">Measurement</div>
    <div class="sidebarComponent">
        <form style="text-align:left; font-size: 12px;">
            <table style="font-size:1.0em;">
                <td id="lastonMouseoverObject_kind_3c2f468c">-</td> <td id="lastonMouseoverObject_index_3c2f468c">-</td> 
                <td id="position_3c2f468c">-</td></tr>
            </table>
            <p id="measure_3c2f468c"></p>
            <p id="error_3c2f468c"></p>
        </form>
    </div>

</div>

<script>
    document.onkeyup = function(e) {
      var x = event.which || event.keyCode;
      var label = 0;
        if (x == 49) {
            set_viewpoint('3c2f468c', 'top_pos', 'top_ori');
        } else if (x == 50) {
            set_viewpoint('3c2f468c', 'front_pos', 'front_ori');
        } else if (x == 51) {
            set_viewpoint('3c2f468c', 'right_pos', 'right_ori');
        } else if (x == 83) {
          spacefilling('3c2f468c');
        } else if (x == 66) {
          ballstick('3c2f468c');
        } else if (x == 80) {
          polyhedra('3c2f468c');
        } else if (x == 52) {
            element('3c2f468c');
        } else if (x == 53) {
            index('3c2f468c');
        } else if (x == 54) {
            none('3c2f468c');
        }
      };
    </script>
 <div class = "column right" > 
   <X3D id = "x3dase" PrimitiveQuality = "high" > 
     <Scene > 
       <Transform id = "t_camera_persp_3c2f468c" rotation = "0 0 0 0" > 
         <Viewpoint id = "camera_persp_3c2f468c" position = "2.4422854091267485 4.2301649660036755 58.14618480413921" centerOfRotation = "2.4422854091267485 4.2301649660036755 19.144754853020494" orientation = "0 0 0 0" description = "camera" > 
         </Viewpoint> 
       </Transform> 
       <Transform id = "t_camera_ortho_3c2f468c" rotation = "0 0 0 0" > 
         <OrthoViewpoint id = "camera_ortho_3c2f468c" position = "2.4422854091267485 4.2301649660036755 58.14618480413921" centerOfRotation = "2.4422854091267485 4.2301649660036755 19.144754853020494" orientation = "0 0 0 0" fieldOfView = "-13.000476650372905 -13.000476650372905 13.000476650372905 13.000476650372905" description = "camera" > 
         </OrthoViewpoint> 
       </Transform> 
       <Group onclick = "handleGroupClick(event, '3c2f468c')" > 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_O" id = "as_O_3c2f468c" > 
             <Appearance DEF = "app_O" > 
               <Material name = "am_3c2f468c" diffuseColor = "1.0 0.051 0.051" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_O" radius = "0.66" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_96_3c2f468c" uuid = "3c2f468c" id = "at_96_3c2f468c" radius = "0.66" name = "at_3c2f468c" translation = "2.55 4.37 26.57" scale = "1 1 1" > 
           <Shape kind = "O" index = "96" uuid = "3c2f468c" > 
             <Appearance USE = "app_O" > 
             </Appearance> 
             <Sphere USE = "asp_O" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_97_3c2f468c" uuid = "3c2f468c" id = "at_97_3c2f468c" radius = "0.66" name = "at_3c2f468c" translation = "3.63 6.31 26.61" scale = "1 1 1" > 
           <Shape kind = "O" index = "97" uuid = "3c2f468c" > 
             <Appearance USE = "app_O" > 
             </Appearance> 
             <Sphere USE = "asp_O" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_H" id = "as_H_3c2f468c" > 
             <Appearance DEF = "app_H" > 
               <Material name = "am_3c2f468c" diffuseColor = "1.0 1.0 1.0" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_H" radius = "0.31" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_99_3c2f468c" uuid = "3c2f468c" id = "at_99_3c2f468c" radius = "0.31" name = "at_3c2f468c" translation = "3.96 7.13 26.92" scale = "1 1 1" > 
           <Shape kind = "H" index = "99" uuid = "3c2f468c" > 
             <Appearance USE = "app_H" > 
             </Appearance> 
             <Sphere USE = "asp_H" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_Cu" id = "as_Cu_3c2f468c" > 
             <Appearance DEF = "app_Cu" > 
               <Material name = "am_3c2f468c" diffuseColor = "0.784 0.502 0.2" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_Cu" radius = "1.32" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_0_3c2f468c" uuid = "3c2f468c" id = "at_0_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-1.3 2.25 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "0" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_1_3c2f468c" uuid = "3c2f468c" id = "at_1_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-0.0 1.5 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "1" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_2_3c2f468c" uuid = "3c2f468c" id = "at_2_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "1.3 0.75 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "2" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_3_3c2f468c" uuid = "3c2f468c" id = "at_3_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "1.29 2.23 24.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "3" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_4_3c2f468c" uuid = "3c2f468c" id = "at_4_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-0.0 1.5 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "4" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_5_3c2f468c" uuid = "3c2f468c" id = "at_5_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "1.3 0.75 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "5" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_6_3c2f468c" uuid = "3c2f468c" id = "at_6_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-2.6 4.5 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "6" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_7_3c2f468c" uuid = "3c2f468c" id = "at_7_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-1.3 3.75 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "7" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_8_3c2f468c" uuid = "3c2f468c" id = "at_8_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "0.0 3.0 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "8" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_9_3c2f468c" uuid = "3c2f468c" id = "at_9_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-0.02 4.5 24.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "9" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_10_3c2f468c" uuid = "3c2f468c" id = "at_10_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-1.3 3.75 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "10" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_11_3c2f468c" uuid = "3c2f468c" id = "at_11_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "0.0 3.0 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "11" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_12_3c2f468c" uuid = "3c2f468c" id = "at_12_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-3.9 6.76 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "12" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_13_3c2f468c" uuid = "3c2f468c" id = "at_13_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-2.6 6.0 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "13" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_14_3c2f468c" uuid = "3c2f468c" id = "at_14_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-1.3 5.25 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "14" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_15_3c2f468c" uuid = "3c2f468c" id = "at_15_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-1.3 6.76 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "15" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_16_3c2f468c" uuid = "3c2f468c" id = "at_16_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-2.6 6.0 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "16" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_17_3c2f468c" uuid = "3c2f468c" id = "at_17_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-1.3 5.25 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "17" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_18_3c2f468c" uuid = "3c2f468c" id = "at_18_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "0.0 0.0 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "18" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_19_3c2f468c" uuid = "3c2f468c" id = "at_19_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-3.9 8.26 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "19" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_20_3c2f468c" uuid = "3c2f468c" id = "at_20_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-2.6 7.51 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "20" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_21_3c2f468c" uuid = "3c2f468c" id = "at_21_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-2.6 9.0 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "21" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_22_3c2f468c" uuid = "3c2f468c" id = "at_22_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-3.9 8.26 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "22" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_23_3c2f468c" uuid = "3c2f468c" id = "at_23_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-2.6 7.51 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "23" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_24_3c2f468c" uuid = "3c2f468c" id = "at_24_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "1.3 2.25 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "24" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_25_3c2f468c" uuid = "3c2f468c" id = "at_25_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "2.6 1.5 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "25" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_26_3c2f468c" uuid = "3c2f468c" id = "at_26_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "3.9 0.75 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "26" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_27_3c2f468c" uuid = "3c2f468c" id = "at_27_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "3.91 2.23 24.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "27" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_28_3c2f468c" uuid = "3c2f468c" id = "at_28_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "2.6 1.5 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "28" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_29_3c2f468c" uuid = "3c2f468c" id = "at_29_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "3.9 0.75 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "29" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_30_3c2f468c" uuid = "3c2f468c" id = "at_30_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "0.0 4.5 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "30" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_31_3c2f468c" uuid = "3c2f468c" id = "at_31_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "1.3 3.75 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "31" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_32_3c2f468c" uuid = "3c2f468c" id = "at_32_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "2.6 3.0 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "32" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_33_3c2f468c" uuid = "3c2f468c" id = "at_33_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "2.59 4.49 24.36" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "33" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_34_3c2f468c" uuid = "3c2f468c" id = "at_34_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "1.3 3.75 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "34" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_35_3c2f468c" uuid = "3c2f468c" id = "at_35_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "2.6 3.0 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "35" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_36_3c2f468c" uuid = "3c2f468c" id = "at_36_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-1.3 6.76 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "36" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_37_3c2f468c" uuid = "3c2f468c" id = "at_37_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-0.0 6.0 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "37" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_38_3c2f468c" uuid = "3c2f468c" id = "at_38_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "1.3 5.25 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "38" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_39_3c2f468c" uuid = "3c2f468c" id = "at_39_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "1.28 6.77 24.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "39" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_40_3c2f468c" uuid = "3c2f468c" id = "at_40_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-0.0 6.0 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "40" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_41_3c2f468c" uuid = "3c2f468c" id = "at_41_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "1.3 5.25 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "41" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_42_3c2f468c" uuid = "3c2f468c" id = "at_42_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "2.6 0.0 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "42" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_43_3c2f468c" uuid = "3c2f468c" id = "at_43_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-1.3 8.26 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "43" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_44_3c2f468c" uuid = "3c2f468c" id = "at_44_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "0.0 7.51 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "44" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_45_3c2f468c" uuid = "3c2f468c" id = "at_45_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "5.2 0.0 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "45" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_46_3c2f468c" uuid = "3c2f468c" id = "at_46_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-1.3 8.26 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "46" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_47_3c2f468c" uuid = "3c2f468c" id = "at_47_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "0.0 7.51 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "47" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_48_3c2f468c" uuid = "3c2f468c" id = "at_48_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "3.9 2.25 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "48" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_49_3c2f468c" uuid = "3c2f468c" id = "at_49_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "5.2 1.5 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "49" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_50_3c2f468c" uuid = "3c2f468c" id = "at_50_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "6.5 0.75 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "50" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_51_3c2f468c" uuid = "3c2f468c" id = "at_51_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "6.5 2.25 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "51" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_52_3c2f468c" uuid = "3c2f468c" id = "at_52_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "5.2 1.5 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "52" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_53_3c2f468c" uuid = "3c2f468c" id = "at_53_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "6.5 0.75 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "53" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_54_3c2f468c" uuid = "3c2f468c" id = "at_54_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "2.6 4.5 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "54" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_55_3c2f468c" uuid = "3c2f468c" id = "at_55_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "3.9 3.75 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "55" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_56_3c2f468c" uuid = "3c2f468c" id = "at_56_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "5.2 3.0 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "56" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_57_3c2f468c" uuid = "3c2f468c" id = "at_57_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "5.23 4.49 24.28" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "57" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_58_3c2f468c" uuid = "3c2f468c" id = "at_58_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "3.9 3.75 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "58" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_59_3c2f468c" uuid = "3c2f468c" id = "at_59_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "5.2 3.0 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "59" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_60_3c2f468c" uuid = "3c2f468c" id = "at_60_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "1.3 6.76 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "60" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_61_3c2f468c" uuid = "3c2f468c" id = "at_61_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "2.6 6.0 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "61" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_62_3c2f468c" uuid = "3c2f468c" id = "at_62_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "3.9 5.25 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "62" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_63_3c2f468c" uuid = "3c2f468c" id = "at_63_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "3.91 6.77 24.34" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "63" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_64_3c2f468c" uuid = "3c2f468c" id = "at_64_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "2.6 6.0 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "64" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_65_3c2f468c" uuid = "3c2f468c" id = "at_65_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "3.9 5.25 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "65" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_66_3c2f468c" uuid = "3c2f468c" id = "at_66_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "5.2 0.0 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "66" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_67_3c2f468c" uuid = "3c2f468c" id = "at_67_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "1.3 8.26 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "67" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_68_3c2f468c" uuid = "3c2f468c" id = "at_68_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "2.6 7.51 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "68" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_69_3c2f468c" uuid = "3c2f468c" id = "at_69_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "7.8 0.02 24.3" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "69" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_70_3c2f468c" uuid = "3c2f468c" id = "at_70_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "1.3 8.26 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "70" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_71_3c2f468c" uuid = "3c2f468c" id = "at_71_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "2.6 7.51 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "71" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_72_3c2f468c" uuid = "3c2f468c" id = "at_72_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "6.5 2.25 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "72" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_73_3c2f468c" uuid = "3c2f468c" id = "at_73_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "7.8 1.5 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "73" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_74_3c2f468c" uuid = "3c2f468c" id = "at_74_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "9.1 0.75 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "74" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_75_3c2f468c" uuid = "3c2f468c" id = "at_75_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "9.1 2.25 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "75" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_76_3c2f468c" uuid = "3c2f468c" id = "at_76_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "7.8 1.5 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "76" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_77_3c2f468c" uuid = "3c2f468c" id = "at_77_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "9.1 0.75 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "77" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_78_3c2f468c" uuid = "3c2f468c" id = "at_78_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "5.2 4.5 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "78" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_79_3c2f468c" uuid = "3c2f468c" id = "at_79_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "6.5 3.75 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "79" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_80_3c2f468c" uuid = "3c2f468c" id = "at_80_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "7.8 3.0 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "80" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_81_3c2f468c" uuid = "3c2f468c" id = "at_81_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-2.6 4.5 24.31" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "81" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_82_3c2f468c" uuid = "3c2f468c" id = "at_82_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "6.5 3.75 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "82" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_83_3c2f468c" uuid = "3c2f468c" id = "at_83_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "7.8 3.0 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "83" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_84_3c2f468c" uuid = "3c2f468c" id = "at_84_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "3.9 6.76 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "84" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_85_3c2f468c" uuid = "3c2f468c" id = "at_85_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "5.2 6.0 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "85" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_86_3c2f468c" uuid = "3c2f468c" id = "at_86_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "6.5 5.25 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "86" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_87_3c2f468c" uuid = "3c2f468c" id = "at_87_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "-3.88 6.76 24.3" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "87" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_88_3c2f468c" uuid = "3c2f468c" id = "at_88_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "5.2 6.0 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "88" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_89_3c2f468c" uuid = "3c2f468c" id = "at_89_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "6.5 5.25 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "89" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_90_3c2f468c" uuid = "3c2f468c" id = "at_90_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "7.8 0.0 18.05" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "90" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_91_3c2f468c" uuid = "3c2f468c" id = "at_91_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "3.9 8.26 13.8" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "91" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_92_3c2f468c" uuid = "3c2f468c" id = "at_92_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "5.2 7.51 15.92" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "92" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_93_3c2f468c" uuid = "3c2f468c" id = "at_93_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "0.01 0.01 24.3" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "93" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_94_3c2f468c" uuid = "3c2f468c" id = "at_94_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "3.9 8.26 20.17" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "94" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Transform DEF = "at_95_3c2f468c" uuid = "3c2f468c" id = "at_95_3c2f468c" radius = "1.32" name = "at_3c2f468c" translation = "5.2 7.51 22.29" scale = "1 1 1" > 
           <Shape kind = "Cu" index = "95" uuid = "3c2f468c" > 
             <Appearance USE = "app_Cu" > 
             </Appearance> 
             <Sphere USE = "asp_Cu" > 
             </Sphere> 
           </Shape> 
         </Transform> 
         <Switch whichChoice = "-1" > 
           <Shape DEF = "as_C" id = "as_C_3c2f468c" > 
             <Appearance DEF = "app_C" > 
               <Material name = "am_3c2f468c" diffuseColor = "0.565 0.565 0.565" transparency = "0.01" > 
               </Material> 
             </Appearance> 
             <Sphere DEF = "asp_C" radius = "0.76" > 
             </Sphere> 
           </Shape> 
         </Switch> 
         <Transform DEF = "at_98_3c2f468c" uuid = "3c2f468c" id = "at_98_3c2f468c" radius = "0.76" name = "at_3c2f468c" translation = "3.03 5.24 27.23" scale = "1 1 1" > 
           <Shape kind = "C" index = "98" uuid = "3c2f468c" > 
             <Appearance USE = "app_C" > 
             </Appearance> 
             <Sphere USE = "asp_C" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Group> 
       <Shape > 
         <IndexedLineSet coordIndex = "0 1 -1 0 2 -1 0 4 -1 1 3 -1 1 5 -1 2 3 -1 2 6 -1 3 7 -1 4 5 -1 4 6 -1 5 7 -1 6 7 -1" > 
           <Coordinate point = "[[ 0.          0.          0.          0.          0.         38.213406
  -5.2001865   9.00698723  0.         -5.2001865   9.00698723 38.213406
  10.400373    0.          0.         10.400373    0.         38.213406
   5.2001865   9.00698723  0.          5.2001865   9.00698723 38.213406  ]]" > 
           </Coordinate> 
         </IndexedLineSet> 
         <Appearance > 
           <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
           </Material> 
         </Appearance> 
       </Shape> 
       <Switch id = "switch_marker_0_3c2f468c" whichChoice = "-1" > 
         <Transform id = "marker_0_3c2f468c" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_1_3c2f468c" whichChoice = "-1" > 
         <Transform id = "marker_1_3c2f468c" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_2_3c2f468c" whichChoice = "-1" > 
         <Transform id = "marker_2_3c2f468c" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_3_3c2f468c" whichChoice = "-1" > 
         <Transform id = "marker_3_3c2f468c" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_marker_4_3c2f468c" whichChoice = "-1" > 
         <Transform id = "marker_4_3c2f468c" scale = ".1 .1 .1" translation = "5 0 0" > 
           <Shape isPickable = "False" > 
             <Appearance > 
               <Material diffuseColor = "#FFD966" transparency = "0.5" > 
               </Material> 
             </Appearance> 
             <Sphere radius = "1.0" > 
             </Sphere> 
           </Shape> 
         </Transform> 
       </Switch> 
       <Switch id = "switch_line_0_3c2f468c" whichChoice = "-1" > 
         <Shape > 
           <IndexedLineSet id = "line_ind_0_3c2f468c" solid = "false" coordIndex = "0 1 -1" > 
             <Coordinate id = "line_coor_0_3c2f468c" point = "0 0 0 0 0 1" > 
             </Coordinate> 
           </IndexedLineSet> 
           <Appearance > 
             <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
             </Material> 
           </Appearance> 
         </Shape> 
       </Switch> 
       <Switch id = "switch_line_1_3c2f468c" whichChoice = "-1" > 
         <Shape > 
           <IndexedLineSet id = "line_ind_1_3c2f468c" solid = "false" coordIndex = "0 1 -1" > 
             <Coordinate id = "line_coor_1_3c2f468c" point = "0 0 0 0 0 1" > 
             </Coordinate> 
           </IndexedLineSet> 
           <Appearance > 
             <Material diffuseColor = "0 0 0" emissiveColor = "0 0.5 1" > 
             </Material> 
           </Appearance> 
         </Shape> 
       </Switch> 
       <TimeSensor DEF = "time" cycleInterval = "8" loop = "true" > 
       </TimeSensor> 
       <PositionInterpolator DEF = "move_0" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-1.300046625 2.2517468075 18.045211008132075 -1.300046625 2.2517468075 18.045211008132075 -1.300046625 2.2517468075 18.045211008132075 -1.300046625 2.2517468075 18.045211008132075 -1.300046625 2.2517468075 18.045211008132075 -1.300046625 2.2517468075 18.045211008132075 -1.300046625 2.2517468075 18.045211008132075 -1.300046625 2.2517468075 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_0" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_0" fromField = "value_changed" toNode = "at_0_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_1" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-5.200186451168819e-06 1.501167540662383 13.799281254066038 -5.200186451168819e-06 1.501167540662383 13.799281254066038 -5.200186451168819e-06 1.501167540662383 13.799281254066038 -5.200186451168819e-06 1.501167540662383 13.799281254066038 -5.200186451168819e-06 1.501167540662383 13.799281254066038 -5.200186451168819e-06 1.501167540662383 13.799281254066038 -5.200186451168819e-06 1.501167540662383 13.799281254066038 -5.200186451168819e-06 1.501167540662383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_1" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_1" fromField = "value_changed" toNode = "at_1_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_2" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "1.3000518251864521 0.7505792668376188 15.922265237801886 1.3000518251864521 0.7505792668376188 15.922265237801886 1.3000518251864521 0.7505792668376188 15.922265237801886 1.3000518251864521 0.7505792668376188 15.922265237801886 1.3000518251864521 0.7505792668376188 15.922265237801886 1.3000518251864521 0.7505792668376188 15.922265237801886 1.3000518251864521 0.7505792668376188 15.922265237801886 1.3000518251864521 0.7505792668376188 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_2" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_2" fromField = "value_changed" toNode = "at_2_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_3" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "1.2868407179450625 2.232170223062522 24.291608700850148 1.2884104246079602 2.235776560509585 24.293679317512733 1.2899801312708576 2.2393828979566477 24.29574993417532 1.2915498379337544 2.24298923540371 24.297820550837905 1.2931195445966521 2.2465955728507727 24.29989116750049 1.2946892512595496 2.250201910297835 24.30196178416311 1.2962589579224462 2.2538082477448977 24.304032400825697 1.2978286645853339 2.25741458519196 24.306103017488283 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_3" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_3" fromField = "value_changed" toNode = "at_3_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_4" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-5.200186451168819e-06 1.501167540662383 20.16819499186792 -5.200186451168819e-06 1.501167540662383 20.16819499186792 -5.200186451168819e-06 1.501167540662383 20.16819499186792 -5.200186451168819e-06 1.501167540662383 20.16819499186792 -5.200186451168819e-06 1.501167540662383 20.16819499186792 -5.200186451168819e-06 1.501167540662383 20.16819499186792 -5.200186451168819e-06 1.501167540662383 20.16819499186792 -5.200186451168819e-06 1.501167540662383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_4" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_4" fromField = "value_changed" toNode = "at_4_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_5" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "1.3000518251864521 0.7505792668376188 22.291140762198115 1.3000518251864521 0.7505792668376188 22.291140762198115 1.3000518251864521 0.7505792668376188 22.291140762198115 1.3000518251864521 0.7505792668376188 22.291140762198115 1.3000518251864521 0.7505792668376188 22.291140762198115 1.3000518251864521 0.7505792668376188 22.291140762198115 1.3000518251864521 0.7505792668376188 22.291140762198115 1.3000518251864521 0.7505792668376188 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_5" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_5" fromField = "value_changed" toNode = "at_5_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_6" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-2.60009325 4.503493615 18.045211008132075 -2.60009325 4.503493615 18.045211008132075 -2.60009325 4.503493615 18.045211008132075 -2.60009325 4.503493615 18.045211008132075 -2.60009325 4.503493615 18.045211008132075 -2.60009325 4.503493615 18.045211008132075 -2.60009325 4.503493615 18.045211008132075 -2.60009325 4.503493615 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_6" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_6" fromField = "value_changed" toNode = "at_6_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_7" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-1.3000518251864512 3.752914348162383 13.799281254066038 -1.3000518251864512 3.752914348162383 13.799281254066038 -1.3000518251864512 3.752914348162383 13.799281254066038 -1.3000518251864512 3.752914348162383 13.799281254066038 -1.3000518251864512 3.752914348162383 13.799281254066038 -1.3000518251864512 3.752914348162383 13.799281254066038 -1.3000518251864512 3.752914348162383 13.799281254066038 -1.3000518251864512 3.752914348162383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_7" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_7" fromField = "value_changed" toNode = "at_7_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_8" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.200186453278242e-06 3.002326074337617 15.922265237801886 5.200186453278242e-06 3.002326074337617 15.922265237801886 5.200186453278242e-06 3.002326074337617 15.922265237801886 5.200186453278242e-06 3.002326074337617 15.922265237801886 5.200186453278242e-06 3.002326074337617 15.922265237801886 5.200186453278242e-06 3.002326074337617 15.922265237801886 5.200186453278242e-06 3.002326074337617 15.922265237801886 5.200186453278242e-06 3.002326074337617 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_8" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_8" fromField = "value_changed" toNode = "at_8_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_9" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-0.02258921733477463 4.501112463412379 24.291553725402593 -0.019547582339861518 4.503104680080789 24.292685099330534 -0.016505947344938196 4.505096896749198 24.293816473258477 -0.013464312350024642 4.507089113417607 24.294947847186418 -0.010422677355100876 4.509081330086017 24.296079221114358 -0.007381042360187766 4.511073546754427 24.2972105950423 -0.00433940736526342 4.513065763422836 24.298341968970284 -0.001297772370350311 4.515057980091246 24.299473342898224 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_9" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_9" fromField = "value_changed" toNode = "at_9_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_10" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-1.3000518251864512 3.752914348162383 20.16819499186792 -1.3000518251864512 3.752914348162383 20.16819499186792 -1.3000518251864512 3.752914348162383 20.16819499186792 -1.3000518251864512 3.752914348162383 20.16819499186792 -1.3000518251864512 3.752914348162383 20.16819499186792 -1.3000518251864512 3.752914348162383 20.16819499186792 -1.3000518251864512 3.752914348162383 20.16819499186792 -1.3000518251864512 3.752914348162383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_10" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_10" fromField = "value_changed" toNode = "at_10_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_11" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.200186453278242e-06 3.002326074337617 22.291140762198115 5.200186453278242e-06 3.002326074337617 22.291140762198115 5.200186453278242e-06 3.002326074337617 22.291140762198115 5.200186453278242e-06 3.002326074337617 22.291140762198115 5.200186453278242e-06 3.002326074337617 22.291140762198115 5.200186453278242e-06 3.002326074337617 22.291140762198115 5.200186453278242e-06 3.002326074337617 22.291140762198115 5.200186453278242e-06 3.002326074337617 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_11" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_11" fromField = "value_changed" toNode = "at_11_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_12" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-3.900139875 6.7552404225 18.045211008132075 -3.900139875 6.7552404225 18.045211008132075 -3.900139875 6.7552404225 18.045211008132075 -3.900139875 6.7552404225 18.045211008132075 -3.900139875 6.7552404225 18.045211008132075 -3.900139875 6.7552404225 18.045211008132075 -3.900139875 6.7552404225 18.045211008132075 -3.900139875 6.7552404225 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_12" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_12" fromField = "value_changed" toNode = "at_12_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_13" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-2.6000984501864512 6.004661155662383 13.799281254066038 -2.6000984501864512 6.004661155662383 13.799281254066038 -2.6000984501864512 6.004661155662383 13.799281254066038 -2.6000984501864512 6.004661155662383 13.799281254066038 -2.6000984501864512 6.004661155662383 13.799281254066038 -2.6000984501864512 6.004661155662383 13.799281254066038 -2.6000984501864512 6.004661155662383 13.799281254066038 -2.6000984501864512 6.004661155662383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_13" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_13" fromField = "value_changed" toNode = "at_13_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_14" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-1.3000414248135468 5.254072881837617 15.922265237801886 -1.3000414248135468 5.254072881837617 15.922265237801886 -1.3000414248135468 5.254072881837617 15.922265237801886 -1.3000414248135468 5.254072881837617 15.922265237801886 -1.3000414248135468 5.254072881837617 15.922265237801886 -1.3000414248135468 5.254072881837617 15.922265237801886 -1.3000414248135468 5.254072881837617 15.922265237801886 -1.3000414248135468 5.254072881837617 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_14" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_14" fromField = "value_changed" toNode = "at_14_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_15" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-1.3026440475666108 6.756406937837952 24.306087735478723 -1.3015623932311542 6.756482425498219 24.305550359255925 -1.3004807388956978 6.7565579131584865 24.305012983033123 -1.2993990845602414 6.756633400818754 24.30447560681032 -1.2983174302247853 6.756708888479022 24.303938230587523 -1.297235775889329 6.756784376139288 24.30340085436472 -1.2961541215538621 6.756859863799556 24.302863478141916 -1.295072467218401 6.756935351459815 24.302326101919117 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_15" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_15" fromField = "value_changed" toNode = "at_15_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_16" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-2.6000984501864512 6.004661155662383 20.16819499186792 -2.6000984501864512 6.004661155662383 20.16819499186792 -2.6000984501864512 6.004661155662383 20.16819499186792 -2.6000984501864512 6.004661155662383 20.16819499186792 -2.6000984501864512 6.004661155662383 20.16819499186792 -2.6000984501864512 6.004661155662383 20.16819499186792 -2.6000984501864512 6.004661155662383 20.16819499186792 -2.6000984501864512 6.004661155662383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_16" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_16" fromField = "value_changed" toNode = "at_16_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_17" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-1.3000414248135468 5.254072881837617 22.291140762198115 -1.3000414248135468 5.254072881837617 22.291140762198115 -1.3000414248135468 5.254072881837617 22.291140762198115 -1.3000414248135468 5.254072881837617 22.291140762198115 -1.3000414248135468 5.254072881837617 22.291140762198115 -1.3000414248135468 5.254072881837617 22.291140762198115 -1.3000414248135468 5.254072881837617 22.291140762198115 -1.3000414248135468 5.254072881837617 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_17" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_17" fromField = "value_changed" toNode = "at_17_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_18" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "0.0 0.0 18.045211008132075 0.0 0.0 18.045211008132075 0.0 0.0 18.045211008132075 0.0 0.0 18.045211008132075 0.0 0.0 18.045211008132075 0.0 0.0 18.045211008132075 0.0 0.0 18.045211008132075 0.0 0.0 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_18" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_18" fromField = "value_changed" toNode = "at_18_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_19" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-3.900145075186451 8.256407963162383 13.799281254066038 -3.900145075186451 8.256407963162383 13.799281254066038 -3.900145075186451 8.256407963162383 13.799281254066038 -3.900145075186451 8.256407963162383 13.799281254066038 -3.900145075186451 8.256407963162383 13.799281254066038 -3.900145075186451 8.256407963162383 13.799281254066038 -3.900145075186451 8.256407963162383 13.799281254066038 -3.900145075186451 8.256407963162383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_19" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_19" fromField = "value_changed" toNode = "at_19_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_20" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-2.6000880498135466 7.505819689337617 15.922265237801886 -2.6000880498135466 7.505819689337617 15.922265237801886 -2.6000880498135466 7.505819689337617 15.922265237801886 -2.6000880498135466 7.505819689337617 15.922265237801886 -2.6000880498135466 7.505819689337617 15.922265237801886 -2.6000880498135466 7.505819689337617 15.922265237801886 -2.6000880498135466 7.505819689337617 15.922265237801886 -2.6000880498135466 7.505819689337617 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_20" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_20" fromField = "value_changed" toNode = "at_20_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_21" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-2.6002576182309265 9.004584025736298 24.305811581203937 -2.6002103720132146 9.005432885106448 24.305800626753054 -2.600163125795487 9.00628174447659 24.305789672302165 2.6000706204222377 0.00014337384673571878 24.305778717851283 2.6001178666399634 0.0009922332168809922 24.3057677634004 2.600165112857677 0.0018410925870271661 24.305756808949514 2.6002123590754027 0.0026899519571724397 24.305745854498593 2.600259605293128 0.003538811327318614 24.30573490004771 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_21" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_21" fromField = "value_changed" toNode = "at_21_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_22" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-3.900145075186451 8.256407963162383 20.16819499186792 -3.900145075186451 8.256407963162383 20.16819499186792 -3.900145075186451 8.256407963162383 20.16819499186792 -3.900145075186451 8.256407963162383 20.16819499186792 -3.900145075186451 8.256407963162383 20.16819499186792 -3.900145075186451 8.256407963162383 20.16819499186792 -3.900145075186451 8.256407963162383 20.16819499186792 -3.900145075186451 8.256407963162383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_22" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_22" fromField = "value_changed" toNode = "at_22_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_23" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-2.6000880498135466 7.505819689337617 22.291140762198115 -2.6000880498135466 7.505819689337617 22.291140762198115 -2.6000880498135466 7.505819689337617 22.291140762198115 -2.6000880498135466 7.505819689337617 22.291140762198115 -2.6000880498135466 7.505819689337617 22.291140762198115 -2.6000880498135466 7.505819689337617 22.291140762198115 -2.6000880498135466 7.505819689337617 22.291140762198115 -2.6000880498135466 7.505819689337617 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_23" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_23" fromField = "value_changed" toNode = "at_23_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_24" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "1.300046625 2.2517468075 18.045211008132075 1.300046625 2.2517468075 18.045211008132075 1.300046625 2.2517468075 18.045211008132075 1.300046625 2.2517468075 18.045211008132075 1.300046625 2.2517468075 18.045211008132075 1.300046625 2.2517468075 18.045211008132075 1.300046625 2.2517468075 18.045211008132075 1.300046625 2.2517468075 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_24" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_24" fromField = "value_changed" toNode = "at_24_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_25" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "2.6000880498135466 1.501167540662383 13.799281254066038 2.6000880498135466 1.501167540662383 13.799281254066038 2.6000880498135466 1.501167540662383 13.799281254066038 2.6000880498135466 1.501167540662383 13.799281254066038 2.6000880498135466 1.501167540662383 13.799281254066038 2.6000880498135466 1.501167540662383 13.799281254066038 2.6000880498135466 1.501167540662383 13.799281254066038 2.6000880498135466 1.501167540662383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_25" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_25" fromField = "value_changed" toNode = "at_25_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_26" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.9001450751864524 0.7505792668376188 15.922265237801886 3.9001450751864524 0.7505792668376188 15.922265237801886 3.9001450751864524 0.7505792668376188 15.922265237801886 3.9001450751864524 0.7505792668376188 15.922265237801886 3.9001450751864524 0.7505792668376188 15.922265237801886 3.9001450751864524 0.7505792668376188 15.922265237801886 3.9001450751864524 0.7505792668376188 15.922265237801886 3.9001450751864524 0.7505792668376188 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_26" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_26" fromField = "value_changed" toNode = "at_26_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_27" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.9115302705359487 2.232687128953798 24.292211401197463 3.9103498963791123 2.236093388765865 24.29392982519495 3.909169522222274 2.239499648577932 24.295648249192443 3.907989148065436 2.242905908389999 24.29736667318989 3.9068087739085993 2.246312168202066 24.29908509718738 3.905628399751766 2.2497184280141242 24.300803521184868 3.904448025594929 2.2531246878261912 24.30252194518232 3.9032676514381017 2.2565309476382582 24.30424036917981 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_27" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_27" fromField = "value_changed" toNode = "at_27_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_28" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "2.6000880498135466 1.501167540662383 20.16819499186792 2.6000880498135466 1.501167540662383 20.16819499186792 2.6000880498135466 1.501167540662383 20.16819499186792 2.6000880498135466 1.501167540662383 20.16819499186792 2.6000880498135466 1.501167540662383 20.16819499186792 2.6000880498135466 1.501167540662383 20.16819499186792 2.6000880498135466 1.501167540662383 20.16819499186792 2.6000880498135466 1.501167540662383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_28" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_28" fromField = "value_changed" toNode = "at_28_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_29" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.9001450751864524 0.7505792668376188 22.291140762198115 3.9001450751864524 0.7505792668376188 22.291140762198115 3.9001450751864524 0.7505792668376188 22.291140762198115 3.9001450751864524 0.7505792668376188 22.291140762198115 3.9001450751864524 0.7505792668376188 22.291140762198115 3.9001450751864524 0.7505792668376188 22.291140762198115 3.9001450751864524 0.7505792668376188 22.291140762198115 3.9001450751864524 0.7505792668376188 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_29" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_29" fromField = "value_changed" toNode = "at_29_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_30" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "0.0 4.503493615 18.045211008132075 0.0 4.503493615 18.045211008132075 0.0 4.503493615 18.045211008132075 0.0 4.503493615 18.045211008132075 0.0 4.503493615 18.045211008132075 0.0 4.503493615 18.045211008132075 0.0 4.503493615 18.045211008132075 0.0 4.503493615 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_30" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_30" fromField = "value_changed" toNode = "at_30_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_31" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "1.3000414248135468 3.752914348162383 13.799281254066038 1.3000414248135468 3.752914348162383 13.799281254066038 1.3000414248135468 3.752914348162383 13.799281254066038 1.3000414248135468 3.752914348162383 13.799281254066038 1.3000414248135468 3.752914348162383 13.799281254066038 1.3000414248135468 3.752914348162383 13.799281254066038 1.3000414248135468 3.752914348162383 13.799281254066038 1.3000414248135468 3.752914348162383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_31" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_31" fromField = "value_changed" toNode = "at_31_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_32" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "2.6000984501864535 3.002326074337617 15.922265237801886 2.6000984501864535 3.002326074337617 15.922265237801886 2.6000984501864535 3.002326074337617 15.922265237801886 2.6000984501864535 3.002326074337617 15.922265237801886 2.6000984501864535 3.002326074337617 15.922265237801886 2.6000984501864535 3.002326074337617 15.922265237801886 2.6000984501864535 3.002326074337617 15.922265237801886 2.6000984501864535 3.002326074337617 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_32" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_32" fromField = "value_changed" toNode = "at_32_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_33" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "2.58933297355313 4.4869629663702195 24.36053172381088 2.5907635729918677 4.487328085018004 24.35080699072026 2.5921941724306055 4.487693203665788 24.341082257629637 2.5936247718693433 4.488058322313572 24.331357524539015 2.59505537130808 4.488423440961356 24.321632791448394 2.596485970746819 4.488788559609141 24.311908058357773 2.5979165701855558 4.489153678256925 24.302183325267155 2.5993471696243042 4.48951879690471 24.29245859217653 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_33" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_33" fromField = "value_changed" toNode = "at_33_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_34" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "1.3000414248135468 3.752914348162383 20.16819499186792 1.3000414248135468 3.752914348162383 20.16819499186792 1.3000414248135468 3.752914348162383 20.16819499186792 1.3000414248135468 3.752914348162383 20.16819499186792 1.3000414248135468 3.752914348162383 20.16819499186792 1.3000414248135468 3.752914348162383 20.16819499186792 1.3000414248135468 3.752914348162383 20.16819499186792 1.3000414248135468 3.752914348162383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_34" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_34" fromField = "value_changed" toNode = "at_34_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_35" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "2.6000984501864535 3.002326074337617 22.291140762198115 2.6000984501864535 3.002326074337617 22.291140762198115 2.6000984501864535 3.002326074337617 22.291140762198115 2.6000984501864535 3.002326074337617 22.291140762198115 2.6000984501864535 3.002326074337617 22.291140762198115 2.6000984501864535 3.002326074337617 22.291140762198115 2.6000984501864535 3.002326074337617 22.291140762198115 2.6000984501864535 3.002326074337617 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_35" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_35" fromField = "value_changed" toNode = "at_35_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_36" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-1.300046625 6.7552404225 18.045211008132075 -1.300046625 6.7552404225 18.045211008132075 -1.300046625 6.7552404225 18.045211008132075 -1.300046625 6.7552404225 18.045211008132075 -1.300046625 6.7552404225 18.045211008132075 -1.300046625 6.7552404225 18.045211008132075 -1.300046625 6.7552404225 18.045211008132075 -1.300046625 6.7552404225 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_36" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_36" fromField = "value_changed" toNode = "at_36_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_37" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-5.200186453278242e-06 6.004661155662383 13.799281254066038 -5.200186453278242e-06 6.004661155662383 13.799281254066038 -5.200186453278242e-06 6.004661155662383 13.799281254066038 -5.200186453278242e-06 6.004661155662383 13.799281254066038 -5.200186453278242e-06 6.004661155662383 13.799281254066038 -5.200186453278242e-06 6.004661155662383 13.799281254066038 -5.200186453278242e-06 6.004661155662383 13.799281254066038 -5.200186453278242e-06 6.004661155662383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_37" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_37" fromField = "value_changed" toNode = "at_37_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_38" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "1.3000518251864532 5.254072881837617 15.922265237801886 1.3000518251864532 5.254072881837617 15.922265237801886 1.3000518251864532 5.254072881837617 15.922265237801886 1.3000518251864532 5.254072881837617 15.922265237801886 1.3000518251864532 5.254072881837617 15.922265237801886 1.3000518251864532 5.254072881837617 15.922265237801886 1.3000518251864532 5.254072881837617 15.922265237801886 1.3000518251864532 5.254072881837617 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_38" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_38" fromField = "value_changed" toNode = "at_38_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_39" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "1.2762147850790653 6.770126024194263 24.286308972726978 1.2800259339277333 6.769794096330223 24.293741740914573 1.2838370827764114 6.769462168466185 24.301174509102125 1.2876482316250801 6.769130240602146 24.30860727728968 1.2914593804737633 6.768798312738098 24.316040045477273 1.295270529322442 6.768466384874059 24.32347281366483 1.2990816781711103 6.76813445701002 24.330905581852377 1.3028928270197888 6.767802529145981 24.338338350039972 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_39" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_39" fromField = "value_changed" toNode = "at_39_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_40" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-5.200186453278242e-06 6.004661155662383 20.16819499186792 -5.200186453278242e-06 6.004661155662383 20.16819499186792 -5.200186453278242e-06 6.004661155662383 20.16819499186792 -5.200186453278242e-06 6.004661155662383 20.16819499186792 -5.200186453278242e-06 6.004661155662383 20.16819499186792 -5.200186453278242e-06 6.004661155662383 20.16819499186792 -5.200186453278242e-06 6.004661155662383 20.16819499186792 -5.200186453278242e-06 6.004661155662383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_40" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_40" fromField = "value_changed" toNode = "at_40_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_41" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "1.3000518251864532 5.254072881837617 22.291140762198115 1.3000518251864532 5.254072881837617 22.291140762198115 1.3000518251864532 5.254072881837617 22.291140762198115 1.3000518251864532 5.254072881837617 22.291140762198115 1.3000518251864532 5.254072881837617 22.291140762198115 1.3000518251864532 5.254072881837617 22.291140762198115 1.3000518251864532 5.254072881837617 22.291140762198115 1.3000518251864532 5.254072881837617 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_41" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_41" fromField = "value_changed" toNode = "at_41_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_42" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "2.60009325 0.0 18.045211008132075 2.60009325 0.0 18.045211008132075 2.60009325 0.0 18.045211008132075 2.60009325 0.0 18.045211008132075 2.60009325 0.0 18.045211008132075 2.60009325 0.0 18.045211008132075 2.60009325 0.0 18.045211008132075 2.60009325 0.0 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_42" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_42" fromField = "value_changed" toNode = "at_42_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_43" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-1.3000518251864532 8.256407963162383 13.799281254066038 -1.3000518251864532 8.256407963162383 13.799281254066038 -1.3000518251864532 8.256407963162383 13.799281254066038 -1.3000518251864532 8.256407963162383 13.799281254066038 -1.3000518251864532 8.256407963162383 13.799281254066038 -1.3000518251864532 8.256407963162383 13.799281254066038 -1.3000518251864532 8.256407963162383 13.799281254066038 -1.3000518251864532 8.256407963162383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_43" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_43" fromField = "value_changed" toNode = "at_43_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_44" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.200186453278242e-06 7.505819689337617 15.922265237801886 5.200186453278242e-06 7.505819689337617 15.922265237801886 5.200186453278242e-06 7.505819689337617 15.922265237801886 5.200186453278242e-06 7.505819689337617 15.922265237801886 5.200186453278242e-06 7.505819689337617 15.922265237801886 5.200186453278242e-06 7.505819689337617 15.922265237801886 5.200186453278242e-06 7.505819689337617 15.922265237801886 5.200186453278242e-06 7.505819689337617 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_44" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_44" fromField = "value_changed" toNode = "at_44_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_45" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.197628788148022 0.0011304780360662174 24.30602729431799 5.19529591887626 0.0031821527690234453 24.305252395006463 5.192963049604489 0.005233827501980674 24.304477495694893 5.190630180332718 0.007285502234937903 24.30370259638332 5.188297311060958 0.009337176967895131 24.30292769707179 5.185964441789187 0.011388851700852359 24.302152797760225 5.183631572517416 0.013440526433809588 24.30137789844869 5.1812987032456554 0.015492201166766814 24.300602999137123 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_45" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_45" fromField = "value_changed" toNode = "at_45_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_46" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-1.3000518251864532 8.256407963162383 20.16819499186792 -1.3000518251864532 8.256407963162383 20.16819499186792 -1.3000518251864532 8.256407963162383 20.16819499186792 -1.3000518251864532 8.256407963162383 20.16819499186792 -1.3000518251864532 8.256407963162383 20.16819499186792 -1.3000518251864532 8.256407963162383 20.16819499186792 -1.3000518251864532 8.256407963162383 20.16819499186792 -1.3000518251864532 8.256407963162383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_46" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_46" fromField = "value_changed" toNode = "at_46_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_47" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.200186453278242e-06 7.505819689337617 22.291140762198115 5.200186453278242e-06 7.505819689337617 22.291140762198115 5.200186453278242e-06 7.505819689337617 22.291140762198115 5.200186453278242e-06 7.505819689337617 22.291140762198115 5.200186453278242e-06 7.505819689337617 22.291140762198115 5.200186453278242e-06 7.505819689337617 22.291140762198115 5.200186453278242e-06 7.505819689337617 22.291140762198115 5.200186453278242e-06 7.505819689337617 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_47" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_47" fromField = "value_changed" toNode = "at_47_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_48" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.900139875 2.2517468075 18.045211008132075 3.900139875 2.2517468075 18.045211008132075 3.900139875 2.2517468075 18.045211008132075 3.900139875 2.2517468075 18.045211008132075 3.900139875 2.2517468075 18.045211008132075 3.900139875 2.2517468075 18.045211008132075 3.900139875 2.2517468075 18.045211008132075 3.900139875 2.2517468075 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_48" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_48" fromField = "value_changed" toNode = "at_48_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_49" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.200181299813547 1.501167540662383 13.799281254066038 5.200181299813547 1.501167540662383 13.799281254066038 5.200181299813547 1.501167540662383 13.799281254066038 5.200181299813547 1.501167540662383 13.799281254066038 5.200181299813547 1.501167540662383 13.799281254066038 5.200181299813547 1.501167540662383 13.799281254066038 5.200181299813547 1.501167540662383 13.799281254066038 5.200181299813547 1.501167540662383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_49" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_49" fromField = "value_changed" toNode = "at_49_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_50" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "6.500238325186452 0.7505792668376188 15.922265237801886 6.500238325186452 0.7505792668376188 15.922265237801886 6.500238325186452 0.7505792668376188 15.922265237801886 6.500238325186452 0.7505792668376188 15.922265237801886 6.500238325186452 0.7505792668376188 15.922265237801886 6.500238325186452 0.7505792668376188 15.922265237801886 6.500238325186452 0.7505792668376188 15.922265237801886 6.500238325186452 0.7505792668376188 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_50" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_50" fromField = "value_changed" toNode = "at_50_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_51" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "6.504248092255007 2.2490619056584444 24.306947052287367 6.5040908258441865 2.250009470660706 24.306291885236494 6.503933559433366 2.2509570356629673 24.305636718185625 6.503776293022542 2.25190460066522 24.304981551134716 6.503619026611721 2.252852165667482 24.30432638408384 6.503461760200901 2.253799730669743 24.30367121703297 6.503304493790081 2.2547472956720047 24.303016049982094 6.503147227379251 2.255694860674266 24.302360882931225 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_51" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_51" fromField = "value_changed" toNode = "at_51_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_52" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.200181299813547 1.501167540662383 20.16819499186792 5.200181299813547 1.501167540662383 20.16819499186792 5.200181299813547 1.501167540662383 20.16819499186792 5.200181299813547 1.501167540662383 20.16819499186792 5.200181299813547 1.501167540662383 20.16819499186792 5.200181299813547 1.501167540662383 20.16819499186792 5.200181299813547 1.501167540662383 20.16819499186792 5.200181299813547 1.501167540662383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_52" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_52" fromField = "value_changed" toNode = "at_52_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_53" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "6.500238325186452 0.7505792668376188 22.291140762198115 6.500238325186452 0.7505792668376188 22.291140762198115 6.500238325186452 0.7505792668376188 22.291140762198115 6.500238325186452 0.7505792668376188 22.291140762198115 6.500238325186452 0.7505792668376188 22.291140762198115 6.500238325186452 0.7505792668376188 22.291140762198115 6.500238325186452 0.7505792668376188 22.291140762198115 6.500238325186452 0.7505792668376188 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_53" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_53" fromField = "value_changed" toNode = "at_53_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_54" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "2.60009325 4.503493615 18.045211008132075 2.60009325 4.503493615 18.045211008132075 2.60009325 4.503493615 18.045211008132075 2.60009325 4.503493615 18.045211008132075 2.60009325 4.503493615 18.045211008132075 2.60009325 4.503493615 18.045211008132075 2.60009325 4.503493615 18.045211008132075 2.60009325 4.503493615 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_54" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_54" fromField = "value_changed" toNode = "at_54_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_55" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.900134674813547 3.752914348162383 13.799281254066038 3.900134674813547 3.752914348162383 13.799281254066038 3.900134674813547 3.752914348162383 13.799281254066038 3.900134674813547 3.752914348162383 13.799281254066038 3.900134674813547 3.752914348162383 13.799281254066038 3.900134674813547 3.752914348162383 13.799281254066038 3.900134674813547 3.752914348162383 13.799281254066038 3.900134674813547 3.752914348162383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_55" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_55" fromField = "value_changed" toNode = "at_55_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_56" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.200191700186454 3.002326074337617 15.922265237801886 5.200191700186454 3.002326074337617 15.922265237801886 5.200191700186454 3.002326074337617 15.922265237801886 5.200191700186454 3.002326074337617 15.922265237801886 5.200191700186454 3.002326074337617 15.922265237801886 5.200191700186454 3.002326074337617 15.922265237801886 5.200191700186454 3.002326074337617 15.922265237801886 5.200191700186454 3.002326074337617 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_56" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_56" fromField = "value_changed" toNode = "at_56_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_57" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.225732764435999 4.488706228919751 24.280283994998204 5.22236101663988 4.491944896918729 24.28295857875546 5.218989268843757 4.4951835649176966 24.28563316251267 5.21561752104763 4.498422232916673 24.28830774626992 5.212245773251511 4.50166090091565 24.290982330027173 5.208874025455388 4.504899568914618 24.293656913784385 5.205502277659271 4.508138236913595 24.29633149754164 5.2021305298631475 4.511376904912564 24.29900608129889 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_57" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_57" fromField = "value_changed" toNode = "at_57_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_58" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.900134674813547 3.752914348162383 20.16819499186792 3.900134674813547 3.752914348162383 20.16819499186792 3.900134674813547 3.752914348162383 20.16819499186792 3.900134674813547 3.752914348162383 20.16819499186792 3.900134674813547 3.752914348162383 20.16819499186792 3.900134674813547 3.752914348162383 20.16819499186792 3.900134674813547 3.752914348162383 20.16819499186792 3.900134674813547 3.752914348162383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_58" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_58" fromField = "value_changed" toNode = "at_58_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_59" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.200191700186454 3.002326074337617 22.291140762198115 5.200191700186454 3.002326074337617 22.291140762198115 5.200191700186454 3.002326074337617 22.291140762198115 5.200191700186454 3.002326074337617 22.291140762198115 5.200191700186454 3.002326074337617 22.291140762198115 5.200191700186454 3.002326074337617 22.291140762198115 5.200191700186454 3.002326074337617 22.291140762198115 5.200191700186454 3.002326074337617 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_59" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_59" fromField = "value_changed" toNode = "at_59_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_60" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "1.300046625 6.7552404225 18.045211008132075 1.300046625 6.7552404225 18.045211008132075 1.300046625 6.7552404225 18.045211008132075 1.300046625 6.7552404225 18.045211008132075 1.300046625 6.7552404225 18.045211008132075 1.300046625 6.7552404225 18.045211008132075 1.300046625 6.7552404225 18.045211008132075 1.300046625 6.7552404225 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_60" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_60" fromField = "value_changed" toNode = "at_60_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_61" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "2.6000880498135466 6.004661155662383 13.799281254066038 2.6000880498135466 6.004661155662383 13.799281254066038 2.6000880498135466 6.004661155662383 13.799281254066038 2.6000880498135466 6.004661155662383 13.799281254066038 2.6000880498135466 6.004661155662383 13.799281254066038 2.6000880498135466 6.004661155662383 13.799281254066038 2.6000880498135466 6.004661155662383 13.799281254066038 2.6000880498135466 6.004661155662383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_61" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_61" fromField = "value_changed" toNode = "at_61_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_62" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.9001450751864537 5.254072881837617 15.922265237801886 3.9001450751864537 5.254072881837617 15.922265237801886 3.9001450751864537 5.254072881837617 15.922265237801886 3.9001450751864537 5.254072881837617 15.922265237801886 3.9001450751864537 5.254072881837617 15.922265237801886 3.9001450751864537 5.254072881837617 15.922265237801886 3.9001450751864537 5.254072881837617 15.922265237801886 3.9001450751864537 5.254072881837617 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_62" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_62" fromField = "value_changed" toNode = "at_62_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_63" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.9089870184276374 6.77458634035061 24.34248198378086 3.907071749762723 6.773629313595785 24.342839742165797 3.905156481097808 6.772672286840959 24.34319750055073 3.903241212432904 6.771715260086133 24.343555258935663 3.9013259437679904 6.770758233331307 24.343913017320595 3.8994106751030766 6.7698012065764805 24.34427077570553 3.8974954064381624 6.768844179821656 24.344628534090425 3.8955801377732473 6.76788715306683 24.344986292475358 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_63" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_63" fromField = "value_changed" toNode = "at_63_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_64" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "2.6000880498135466 6.004661155662383 20.16819499186792 2.6000880498135466 6.004661155662383 20.16819499186792 2.6000880498135466 6.004661155662383 20.16819499186792 2.6000880498135466 6.004661155662383 20.16819499186792 2.6000880498135466 6.004661155662383 20.16819499186792 2.6000880498135466 6.004661155662383 20.16819499186792 2.6000880498135466 6.004661155662383 20.16819499186792 2.6000880498135466 6.004661155662383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_64" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_64" fromField = "value_changed" toNode = "at_64_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_65" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.9001450751864537 5.254072881837617 22.291140762198115 3.9001450751864537 5.254072881837617 22.291140762198115 3.9001450751864537 5.254072881837617 22.291140762198115 3.9001450751864537 5.254072881837617 22.291140762198115 3.9001450751864537 5.254072881837617 22.291140762198115 3.9001450751864537 5.254072881837617 22.291140762198115 3.9001450751864537 5.254072881837617 22.291140762198115 3.9001450751864537 5.254072881837617 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_65" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_65" fromField = "value_changed" toNode = "at_65_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_66" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.2001865 0.0 18.045211008132075 5.2001865 0.0 18.045211008132075 5.2001865 0.0 18.045211008132075 5.2001865 0.0 18.045211008132075 5.2001865 0.0 18.045211008132075 5.2001865 0.0 18.045211008132075 5.2001865 0.0 18.045211008132075 5.2001865 0.0 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_66" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_66" fromField = "value_changed" toNode = "at_66_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_67" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "1.3000414248135468 8.256407963162383 13.799281254066038 1.3000414248135468 8.256407963162383 13.799281254066038 1.3000414248135468 8.256407963162383 13.799281254066038 1.3000414248135468 8.256407963162383 13.799281254066038 1.3000414248135468 8.256407963162383 13.799281254066038 1.3000414248135468 8.256407963162383 13.799281254066038 1.3000414248135468 8.256407963162383 13.799281254066038 1.3000414248135468 8.256407963162383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_67" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_67" fromField = "value_changed" toNode = "at_67_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_68" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "2.600098450186454 7.505819689337617 15.922265237801886 2.600098450186454 7.505819689337617 15.922265237801886 2.600098450186454 7.505819689337617 15.922265237801886 2.600098450186454 7.505819689337617 15.922265237801886 2.600098450186454 7.505819689337617 15.922265237801886 2.600098450186454 7.505819689337617 15.922265237801886 2.600098450186454 7.505819689337617 15.922265237801886 2.600098450186454 7.505819689337617 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_68" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_68" fromField = "value_changed" toNode = "at_68_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_69" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "7.796609318357016 0.015412079261222214 24.300599731513017 7.797085350054979 0.013412800810375472 24.305894635888475 7.797561381752941 0.011413522359527828 24.311189540263932 7.798037413450904 0.009414243908681086 24.31648444463935 7.798513445148866 0.007414965457833442 24.321779349014808 7.798989476846828 0.0054156870069867 24.32707425339027 7.7994655085448015 0.003416408556139056 24.332369157765722 7.799941540242764 0.0014171301052923134 24.33766406214118 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_69" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_69" fromField = "value_changed" toNode = "at_69_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_70" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "1.3000414248135468 8.256407963162383 20.16819499186792 1.3000414248135468 8.256407963162383 20.16819499186792 1.3000414248135468 8.256407963162383 20.16819499186792 1.3000414248135468 8.256407963162383 20.16819499186792 1.3000414248135468 8.256407963162383 20.16819499186792 1.3000414248135468 8.256407963162383 20.16819499186792 1.3000414248135468 8.256407963162383 20.16819499186792 1.3000414248135468 8.256407963162383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_70" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_70" fromField = "value_changed" toNode = "at_70_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_71" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "2.600098450186454 7.505819689337617 22.291140762198115 2.600098450186454 7.505819689337617 22.291140762198115 2.600098450186454 7.505819689337617 22.291140762198115 2.600098450186454 7.505819689337617 22.291140762198115 2.600098450186454 7.505819689337617 22.291140762198115 2.600098450186454 7.505819689337617 22.291140762198115 2.600098450186454 7.505819689337617 22.291140762198115 2.600098450186454 7.505819689337617 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_71" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_71" fromField = "value_changed" toNode = "at_71_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_72" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "6.500233124999999 2.2517468075 18.045211008132075 6.500233124999999 2.2517468075 18.045211008132075 6.500233124999999 2.2517468075 18.045211008132075 6.500233124999999 2.2517468075 18.045211008132075 6.500233124999999 2.2517468075 18.045211008132075 6.500233124999999 2.2517468075 18.045211008132075 6.500233124999999 2.2517468075 18.045211008132075 6.500233124999999 2.2517468075 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_72" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_72" fromField = "value_changed" toNode = "at_72_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_73" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "7.8002745498135475 1.501167540662383 13.799281254066038 7.8002745498135475 1.501167540662383 13.799281254066038 7.8002745498135475 1.501167540662383 13.799281254066038 7.8002745498135475 1.501167540662383 13.799281254066038 7.8002745498135475 1.501167540662383 13.799281254066038 7.8002745498135475 1.501167540662383 13.799281254066038 7.8002745498135475 1.501167540662383 13.799281254066038 7.8002745498135475 1.501167540662383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_73" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_73" fromField = "value_changed" toNode = "at_73_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_74" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "9.100331575186454 0.7505792668376188 15.922265237801886 9.100331575186454 0.7505792668376188 15.922265237801886 9.100331575186454 0.7505792668376188 15.922265237801886 9.100331575186454 0.7505792668376188 15.922265237801886 9.100331575186454 0.7505792668376188 15.922265237801886 9.100331575186454 0.7505792668376188 15.922265237801886 9.100331575186454 0.7505792668376188 15.922265237801886 9.100331575186454 0.7505792668376188 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_74" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_74" fromField = "value_changed" toNode = "at_74_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_75" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "9.097174200896745 2.250940991137997 24.306753681847603 9.097333492634721 2.2518632748519867 24.30609383570946 9.097492784372687 2.2527855585659764 24.305433989571355 9.097652076110652 2.253707842279966 24.304774143433214 9.09781136784862 2.254630125993956 24.30411429729507 9.097970659586585 2.2555524097079456 24.303454451156927 -1.3022430486754517 2.2564746934219446 24.302794605018782 -1.302083756937484 2.2573969771359343 24.30213475888064 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_75" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_75" fromField = "value_changed" toNode = "at_75_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_76" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "7.8002745498135475 1.501167540662383 20.16819499186792 7.8002745498135475 1.501167540662383 20.16819499186792 7.8002745498135475 1.501167540662383 20.16819499186792 7.8002745498135475 1.501167540662383 20.16819499186792 7.8002745498135475 1.501167540662383 20.16819499186792 7.8002745498135475 1.501167540662383 20.16819499186792 7.8002745498135475 1.501167540662383 20.16819499186792 7.8002745498135475 1.501167540662383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_76" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_76" fromField = "value_changed" toNode = "at_76_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_77" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "9.100331575186454 0.7505792668376188 22.291140762198115 9.100331575186454 0.7505792668376188 22.291140762198115 9.100331575186454 0.7505792668376188 22.291140762198115 9.100331575186454 0.7505792668376188 22.291140762198115 9.100331575186454 0.7505792668376188 22.291140762198115 9.100331575186454 0.7505792668376188 22.291140762198115 9.100331575186454 0.7505792668376188 22.291140762198115 9.100331575186454 0.7505792668376188 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_77" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_77" fromField = "value_changed" toNode = "at_77_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_78" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.200186499999999 4.503493615 18.045211008132075 5.200186499999999 4.503493615 18.045211008132075 5.200186499999999 4.503493615 18.045211008132075 5.200186499999999 4.503493615 18.045211008132075 5.200186499999999 4.503493615 18.045211008132075 5.200186499999999 4.503493615 18.045211008132075 5.200186499999999 4.503493615 18.045211008132075 5.200186499999999 4.503493615 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_78" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_78" fromField = "value_changed" toNode = "at_78_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_79" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "6.500227924813547 3.752914348162383 13.799281254066038 6.500227924813547 3.752914348162383 13.799281254066038 6.500227924813547 3.752914348162383 13.799281254066038 6.500227924813547 3.752914348162383 13.799281254066038 6.500227924813547 3.752914348162383 13.799281254066038 6.500227924813547 3.752914348162383 13.799281254066038 6.500227924813547 3.752914348162383 13.799281254066038 6.500227924813547 3.752914348162383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_79" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_79" fromField = "value_changed" toNode = "at_79_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_80" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "7.800284950186454 3.002326074337617 15.922265237801886 7.800284950186454 3.002326074337617 15.922265237801886 7.800284950186454 3.002326074337617 15.922265237801886 7.800284950186454 3.002326074337617 15.922265237801886 7.800284950186454 3.002326074337617 15.922265237801886 7.800284950186454 3.002326074337617 15.922265237801886 7.800284950186454 3.002326074337617 15.922265237801886 7.800284950186454 3.002326074337617 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_80" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_80" fromField = "value_changed" toNode = "at_80_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_81" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-2.5996780909763006 4.502855984801639 24.307534651245753 -2.599687369738151 4.503587154513018 24.307232089782048 -2.5996966485 4.504318324224397 24.306929528318296 -2.5997059272618506 4.505049493935776 24.306626966854587 -2.599715206023701 4.5057806636471565 24.306324405390843 -2.599724484785556 4.5065118333585445 24.30602184392713 -2.5997337635474054 4.507243003069924 24.30571928246342 -2.599743042309255 4.507974172781303 24.305416720999673 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_81" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_81" fromField = "value_changed" toNode = "at_81_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_82" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "6.500227924813547 3.752914348162383 20.16819499186792 6.500227924813547 3.752914348162383 20.16819499186792 6.500227924813547 3.752914348162383 20.16819499186792 6.500227924813547 3.752914348162383 20.16819499186792 6.500227924813547 3.752914348162383 20.16819499186792 6.500227924813547 3.752914348162383 20.16819499186792 6.500227924813547 3.752914348162383 20.16819499186792 6.500227924813547 3.752914348162383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_82" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_82" fromField = "value_changed" toNode = "at_82_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_83" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "7.800284950186454 3.002326074337617 22.291140762198115 7.800284950186454 3.002326074337617 22.291140762198115 7.800284950186454 3.002326074337617 22.291140762198115 7.800284950186454 3.002326074337617 22.291140762198115 7.800284950186454 3.002326074337617 22.291140762198115 7.800284950186454 3.002326074337617 22.291140762198115 7.800284950186454 3.002326074337617 22.291140762198115 7.800284950186454 3.002326074337617 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_83" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_83" fromField = "value_changed" toNode = "at_83_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_84" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.900139875 6.7552404225 18.045211008132075 3.900139875 6.7552404225 18.045211008132075 3.900139875 6.7552404225 18.045211008132075 3.900139875 6.7552404225 18.045211008132075 3.900139875 6.7552404225 18.045211008132075 3.900139875 6.7552404225 18.045211008132075 3.900139875 6.7552404225 18.045211008132075 3.900139875 6.7552404225 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_84" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_84" fromField = "value_changed" toNode = "at_84_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_85" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.200181299813547 6.004661155662383 13.799281254066038 5.200181299813547 6.004661155662383 13.799281254066038 5.200181299813547 6.004661155662383 13.799281254066038 5.200181299813547 6.004661155662383 13.799281254066038 5.200181299813547 6.004661155662383 13.799281254066038 5.200181299813547 6.004661155662383 13.799281254066038 5.200181299813547 6.004661155662383 13.799281254066038 5.200181299813547 6.004661155662383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_85" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_85" fromField = "value_changed" toNode = "at_85_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_86" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "6.500238325186454 5.254072881837617 15.922265237801886 6.500238325186454 5.254072881837617 15.922265237801886 6.500238325186454 5.254072881837617 15.922265237801886 6.500238325186454 5.254072881837617 15.922265237801886 6.500238325186454 5.254072881837617 15.922265237801886 6.500238325186454 5.254072881837617 15.922265237801886 6.500238325186454 5.254072881837617 15.922265237801886 6.500238325186454 5.254072881837617 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_86" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_86" fromField = "value_changed" toNode = "at_86_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_87" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "-3.8848465241626196 6.757014780282923 24.295246580241766 -3.887125047131193 6.7570154902540525 24.295902806527746 -3.8894035700997667 6.7570162002251815 24.296559032813725 -3.8916820930683396 6.757016910196312 24.297215259099705 -3.893960616036907 6.757017620167432 24.297871485385723 -3.8962391390054814 6.757018330138562 24.298527711671703 -3.898517661974054 6.757019040109692 24.299183937957686 -3.900796184942622 6.757019750080813 24.299840164243665 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_87" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_87" fromField = "value_changed" toNode = "at_87_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_88" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.200181299813547 6.004661155662383 20.16819499186792 5.200181299813547 6.004661155662383 20.16819499186792 5.200181299813547 6.004661155662383 20.16819499186792 5.200181299813547 6.004661155662383 20.16819499186792 5.200181299813547 6.004661155662383 20.16819499186792 5.200181299813547 6.004661155662383 20.16819499186792 5.200181299813547 6.004661155662383 20.16819499186792 5.200181299813547 6.004661155662383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_88" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_88" fromField = "value_changed" toNode = "at_88_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_89" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "6.500238325186454 5.254072881837617 22.291140762198115 6.500238325186454 5.254072881837617 22.291140762198115 6.500238325186454 5.254072881837617 22.291140762198115 6.500238325186454 5.254072881837617 22.291140762198115 6.500238325186454 5.254072881837617 22.291140762198115 6.500238325186454 5.254072881837617 22.291140762198115 6.500238325186454 5.254072881837617 22.291140762198115 6.500238325186454 5.254072881837617 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_89" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_89" fromField = "value_changed" toNode = "at_89_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_90" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "7.80027975 0.0 18.045211008132075 7.80027975 0.0 18.045211008132075 7.80027975 0.0 18.045211008132075 7.80027975 0.0 18.045211008132075 7.80027975 0.0 18.045211008132075 7.80027975 0.0 18.045211008132075 7.80027975 0.0 18.045211008132075 7.80027975 0.0 18.045211008132075 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_90" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_90" fromField = "value_changed" toNode = "at_90_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_91" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.9001346748135473 8.256407963162383 13.799281254066038 3.9001346748135473 8.256407963162383 13.799281254066038 3.9001346748135473 8.256407963162383 13.799281254066038 3.9001346748135473 8.256407963162383 13.799281254066038 3.9001346748135473 8.256407963162383 13.799281254066038 3.9001346748135473 8.256407963162383 13.799281254066038 3.9001346748135473 8.256407963162383 13.799281254066038 3.9001346748135473 8.256407963162383 13.799281254066038 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_91" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_91" fromField = "value_changed" toNode = "at_91_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_92" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.200191700186454 7.505819689337617 15.922265237801886 5.200191700186454 7.505819689337617 15.922265237801886 5.200191700186454 7.505819689337617 15.922265237801886 5.200191700186454 7.505819689337617 15.922265237801886 5.200191700186454 7.505819689337617 15.922265237801886 5.200191700186454 7.505819689337617 15.922265237801886 5.200191700186454 7.505819689337617 15.922265237801886 5.200191700186454 7.505819689337617 15.922265237801886 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_92" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_92" fromField = "value_changed" toNode = "at_92_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_93" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "0.006182297954993398 0.009580184696638025 24.297940316260195 0.008001062875383193 0.010600540785778139 24.29804729768584 0.009819827795774028 0.011620896874918255 24.298154279111447 0.01163859271616382 0.012641252964058368 24.298261260537092 0.013457357636554658 0.013661609053198482 24.298368241962702 0.015276122556944452 0.014681965142338597 24.298475223388348 0.017094887477335806 0.01570232123147781 24.298582204813954 0.0189136523977256 0.016722677320617926 24.2986891862396 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_93" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_93" fromField = "value_changed" toNode = "at_93_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_94" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.9001346748135473 8.256407963162383 20.16819499186792 3.9001346748135473 8.256407963162383 20.16819499186792 3.9001346748135473 8.256407963162383 20.16819499186792 3.9001346748135473 8.256407963162383 20.16819499186792 3.9001346748135473 8.256407963162383 20.16819499186792 3.9001346748135473 8.256407963162383 20.16819499186792 3.9001346748135473 8.256407963162383 20.16819499186792 3.9001346748135473 8.256407963162383 20.16819499186792 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_94" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_94" fromField = "value_changed" toNode = "at_94_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_95" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "5.200191700186454 7.505819689337617 22.291140762198115 5.200191700186454 7.505819689337617 22.291140762198115 5.200191700186454 7.505819689337617 22.291140762198115 5.200191700186454 7.505819689337617 22.291140762198115 5.200191700186454 7.505819689337617 22.291140762198115 5.200191700186454 7.505819689337617 22.291140762198115 5.200191700186454 7.505819689337617 22.291140762198115 5.200191700186454 7.505819689337617 22.291140762198115 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_95" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_95" fromField = "value_changed" toNode = "at_95_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_96" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "2.5504261414755263 4.36743410243995 26.574213083472458 2.4762741739080747 4.189932884667673 26.70994291321231 2.4021222063406324 4.012431666895397 26.845672742952164 2.327970238773174 3.8349304491231297 26.981402572692016 2.2538182712057235 3.657429231350853 27.117132402431864 2.1796663036382706 3.4799280135785766 27.252862232171754 2.105514336070818 3.3024267958063 27.388592061911606 2.0313623685033715 3.124925578034033 27.524321891651457 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_96" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_96" fromField = "value_changed" toNode = "at_96_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_97" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.629036388068944 6.3139126463954875 26.6050818053177 3.5060205956752903 6.174596332046562 26.733478158315112 3.383004803281637 6.0352800176976364 26.861874511312486 3.259989010887977 5.895963703348721 26.990270864309895 3.1369732184943246 5.756647388999796 27.118667217307312 3.0139574261006805 5.61733107465087 27.247063570304682 2.8909416337070226 5.478014760301954 27.375459923302095 2.7679258413133683 5.338698445953029 27.50385627629951 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_97" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_97" fromField = "value_changed" toNode = "at_97_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_98" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.0338996681237167 5.244868605808563 27.233820157280345 2.9422945361752824 5.098435744736443 27.285269374494515 2.850689404226839 4.9520028836643215 27.33671859170869 2.7590842722783915 4.8055700225922084 27.388167808922894 2.667479140329948 4.659137161520087 27.439617026137068 2.5758740083815086 4.512704300447976 27.491066243351238 2.4842688764330654 4.366271439375855 27.542515460565408 2.3926637444846177 4.219838578303742 27.59396467777958 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_98" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_98" fromField = "value_changed" toNode = "at_98_3c2f468c" toField = "translation" > 
       </ROUTE> 
       <PositionInterpolator DEF = "move_99" key = " 0.0 0.125 0.25 0.375 0.5 0.625 0.75 0.875" keyValue = "3.9558263936573526 7.130407392282891 26.917919391558485 3.786061563665821 7.178954475292487 26.70831087820422 3.6162967336742757 7.227501558302091 26.49870236484995 3.4465319036827444 7.276048641311687 26.289093851495682 3.2767670736912082 7.3245957243212905 26.079485338141417 3.107002243699667 7.373142807330887 25.86987682478715 2.937237413708131 7.421689890340491 25.66026831143288 2.7674725837165908 7.470236973350087 25.450659798078615 " > 
       </PositionInterpolator> 
       <ROUTE fromNode = "time" fromField = "fraction_changed" toNode = "move_99" toField = "set_fraction" > 
       </ROUTE> 
       <ROUTE fromNode = "move_99" fromField = "value_changed" toNode = "at_99_3c2f468c" toField = "translation" > 
       </ROUTE> 
     </Scene> 
   </X3D> 
 </div> 

<script type="text/javascript" src="https://code.jquery.com/jquery-2.1.0.min.js" ></script>
 <script >
 if (atoms_dict == undefined) {var atoms_dict = {"new": true};}; 
atoms_dict["3c2f468c"] = {com: "2.44 4.23 19.14", 
top_pos: "2.44 4.23 58.15", 
front_pos: "2.44 -34.77 19.14", 
right_pos: "41.44 4.23 19.14", 
left_pos: "-36.56 4.23 19.14", 
top_ori: "0 0 0 0", 
front_ori: "1 0 0 1.57079", 
right_ori: "0 1 0 1.57079", 
left_ori: "0 1 0 -1.57079", 
select: [], 
uuid: "3c2f468c", 
label: "False", 
bond: "False", 
polyhedra: {}, 
};

// if (atoms_dict[uuid]['new']){
function setQuality(uuid, quality){
        // $('#error_'.concat(atoms_dict[uuid]['uuid'])).html('uuid: '.concat(atoms_dict[uuid]["uuid"]));
        var x3d = 'x3dase_' + uuid;
        document.getElementById(x3d).setAttribute('PrimitiveQuality', quality);
    }
function set_viewpoint(uuid, pos, ori){
    // $('#error_'.concat(atoms_dict[uuid]['uuid'])).html('uuid: '.concat(atoms_dict[uuid]["uuid"]));
    var persp = 'camera_persp_' + uuid;
    var ortho = 'camera_ortho_' + uuid;
    document.getElementById(persp).setAttribute('orientation', atoms_dict[uuid][ori]);
    document.getElementById(persp).setAttribute('position', atoms_dict[uuid][pos]);
    document.getElementById(ortho).setAttribute('orientation', atoms_dict[uuid][ori]);
    document.getElementById(ortho).setAttribute('position', atoms_dict[uuid][pos]);
}

//Round a float value to x.xx format
function roundWithTwoDecimals(value)
{
    var x = (Math.round(value * 100)) / 100;
    var y = x.toFixed(2);
    return y;
}
//Handle click on any group member
function handleGroupClick(event)
{
    //Mark hitting point
    var target = event.target;
    var uuid = target.parentNode.getAttribute('uuid')
    var radius = target.parentNode.getAttribute('radius');
    var scale = target.parentNode.getAttribute('scale');
    scale = parseFloat(radius)*parseFloat(scale)*1.2;
    var scale = ' ' + scale + ' ' + scale + ' ' + scale;
    var translation = target.parentNode.getAttribute('translation');
    var id = target.parentNode.getAttribute('id');
    if (window.event.ctrlKey) {
        atoms_dict[uuid]['select'].push(id);

    }
    else {
        for (var i=1; i<= atoms_dict[uuid]['select'].length; i++) {
            $('#switch_marker_' + i + '_' + uuid).attr('whichChoice', -1);
        }
        atoms_dict[uuid]['select'] = [];
        atoms_dict[uuid]['select'].push(id);
        $('#switch_marker_' + 2 + '_' + uuid).attr('whichChoice', -1);
}
    var n = atoms_dict[uuid]['select'].length;
    $('#switch_marker_' + n + '_' + uuid).attr('whichChoice', 0);
    $('#marker_' + n + '_' + uuid).attr('translation', translation);
    $('#marker_' + n + '_' + uuid).attr('scale', scale);
    var atom_kind = '#lastonMouseoverObject_kind_'.concat(uuid);
    var atom_index = '#lastonMouseoverObject_index_'.concat(uuid);
    $(atom_kind).html(target.getAttribute("kind"));
    $(atom_index).html(target.getAttribute("index"));
    //
    var coord = translation.split(" ")
    var atom_position = '#position_'.concat(uuid);
    var x = roundWithTwoDecimals(coord[0]);
    var y = roundWithTwoDecimals(coord[1]);
    var z = roundWithTwoDecimals(coord[2]);
    var position = 'x = ' + x + ' y = ' + y + ' z = ' + z;
    $(atom_position).html(position);

    if (atoms_dict[uuid]['select'].length == 2){
        calculate_distance(uuid);
        draw_line(uuid);
    }
    else if (atoms_dict[uuid]['select'].length == 3){
        calculate_angle(uuid);
        draw_line(uuid);
    }

    console.log(event);
}
//Add a onMouseover callback to every shape
$(document).ready(function(){
    $("shape").each(function() {
        // $(this).attr("onMouseover", "handleOnMouseover_shape(this)");
        // $(this).attr("onclick", "handleClick_shape(this)");
    });
    //Add a onMouseover callback to every transform
    $("transform").each(function() {
        // $(this).attr("onMouseover", "handleOnMouseover_transform(this)");
        // $(this).attr("onclick", "handleClick_transform(this)");
    });
});
$(document).on("click", function(e) {
    if (e.target === document || e.target.tagName === "BODY" || e.target.tagName === "HTML") {
        $('#marker').attr('scale', "0.0001 0.0001 0.0001");
    }
});
//Handle onMouseover on a shape
function handleOnMouseover_shape(shape)
{
    var atom_kind = '#lastonMouseoverObject_kind_'.concat($(shape).attr("uuid"));
    var atom_index = '#lastonMouseoverObject_index_'.concat($(shape).attr("uuid"));
    $(atom_kind).html($(shape).attr("kind"));
    $(atom_index).html($(shape).attr("index"));
}
//Handle onMouseover on a shape
function handleClick_shape(shape)
{
    var atom_kind = '#lastonMouseoverObject_kind_'.concat($(shape).attr("uuid"));
    var atom_index = '#lastonMouseoverObject_index_'.concat($(shape).attr("uuid"));
    $(atom_kind).html($(shape).attr("kind"));
    $(atom_index).html($(shape).attr("index"));
}
//Handle onMouseover on a transform
function handleOnMouseover_transform(transform)
{
    var atom_position = '#position_'.concat($(transform).attr("uuid"));
    var coord = $(transform).attr("translation").split(" "[0]);
    var x = roundWithTwoDecimals(coord[0]);
    var y = roundWithTwoDecimals(coord[1]);
    var z = roundWithTwoDecimals(coord[2]);
    var position = 'x = ' + x + ' y = ' + y + ' z = ' + z;
    $(atom_position).html(position);
}

function calculate_distance(uuid)
{
    var measure = '#measure_'.concat(uuid);
    var c1 = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation").split(" ");
    var c2 = document.getElementById(atoms_dict[uuid]['select'][1]).getAttribute("translation").split(" ");
    r = (c1[0] - c2[0])*(c1[0] - c2[0]) + (c1[1] - c2[1])*(c1[1] - c2[1]) + (c1[2] - c2[2])*(c1[2] - c2[2]);
    r = roundWithTwoDecimals(Math.sqrt(r));
    var dist = 'Distance:  ' + r;
    $(measure).html(dist);
}
function calculate_angle(uuid)
{
    var measure = '#measure_'.concat(uuid);
    var c1 = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation").split(" ");
    var c2 = document.getElementById(atoms_dict[uuid]['select'][1]).getAttribute("translation").split(" ");
    var c3 = document.getElementById(atoms_dict[uuid]['select'][2]).getAttribute("translation").split(" ");
    var AB = Math.sqrt(Math.pow(c2[0]-c1[0],2)+ Math.pow(c2[1]-c1[1],2) + Math.pow(c2[2]-c1[2],2));    
    var BC = Math.sqrt(Math.pow(c2[0]-c3[0],2)+ Math.pow(c2[1]-c3[1],2) + Math.pow(c2[2]-c3[2],2)); 
    var AC = Math.sqrt(Math.pow(c3[0]-c1[0],2)+ Math.pow(c3[1]-c1[1],2)+ Math.pow(c3[2]-c1[2],2));
    var angle = roundWithTwoDecimals(Math.acos((BC*BC+AB*AB-AC*AC)/(2*BC*AB))*180/3.1415926);
    var angle = 'angle:  ' + angle;
    $(measure).html(angle);
}
function draw_line(uuid)
{
    var n = atoms_dict[uuid]['select'].length;
    var coordIndex = '';
    var point = document.getElementById(atoms_dict[uuid]['select'][0]).getAttribute("translation");
    for (var i = 1; i < n; i++) {
        var c1 = document.getElementById(atoms_dict[uuid]['select'][i]).getAttribute("translation");
        coordIndex = coordIndex + (i-1) + ' ' + i + ' -1 ';
        point = point + ' ' + c1 + ' ';
    }
    $('#line_coor_' + 0 + '_' + uuid).attr('point', point);
    $('#line_ind_' + 0 + '_' + uuid).attr('coordIndex', coordIndex);
    $('#switch_line_' + 0 + '_' + uuid).attr('whichChoice', 0);
}
//Handle models
function spacefilling(uuid)
{
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "1.0, 1.0, 1.0");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function ballstick(uuid)
{
    if (atoms_dict[uuid]['bond']=='False'){ 
        alert('Please set bond parameter in your code, e.g. bond=1.0!');
        $('#error_'.concat(uuid)).html('(^_^) Please set bond parameter in your code, e.g. bond=1.0!');
		return ;
    }
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "0.6, 0.6, 0.6");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function polyhedra(uuid)
{
    if (atoms_dict[uuid]['polyhedra'].length==0){ 
        alert('Please set polyhedra parameter in your code, e.g. polyhedra={"Ti": ["O"]}!');
        $('#error_'.concat(uuid)).html('(^_^) Please set polyhedra parameter in your code, e.g. polyhedra={"Ti": ["O"]}!');
		return ;
    }
    var objs = document.getElementsByName(''.concat('at_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("scale", "0.6, 0.6, 0.6");
        }
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ps_'.concat(uuid)).setAttribute("whichChoice", '0');
}
function none(uuid)
{
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.0");
        }
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
        
function element(uuid)
{
    if (atoms_dict[uuid]['label']=='False'){ 
        alert('To show element, please set label=True in your code!');
        $('#error_'.concat(uuid)).html('(^_^) To show element, please set label=True in your code!');
		return ;
	}
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.4");
        }
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
function index(uuid)
{
    if (atoms_dict[uuid]['label']=='False'){ 
        alert('To show index, please set label=True in your code!');
        $('#error_'.concat(uuid)).html('(^_^) To show index, please set label=True in your code!');
		return ;
	}
    var objs = document.getElementsByName(''.concat('am_'.concat(uuid)));
    var max=objs.length;
    for (var i=0; i< max; i++) {
        objs[i].setAttribute("transparency", "0.4");
        }
    document.getElementById('ind_'.concat(uuid)).setAttribute("whichChoice", '0');
    document.getElementById('ele_'.concat(uuid)).setAttribute("whichChoice", '-1');
    document.getElementById('bs_'.concat(uuid)).setAttribute("whichChoice", '-1');
}
// } 
</script> </body>
</html>
